-- ============================================================================
-- CUSTOMS CODE MIGRATION FOR FTP CONNECTIVITY
-- ============================================================================
-- This migration adds test customs codes to existing companies for FTP testing
-- Run this script if you already have the portal installed and need to add
-- customs codes for FTP connectivity testing
-- ============================================================================

-- Ensure customs_code column exists in companies table (should already exist)
-- This is just a safety check for older installations
ALTER TABLE companies 
ADD COLUMN IF NOT EXISTS customs_code VARCHAR(100) DEFAULT NULL;

-- Add index for customs codes for better performance
ALTER TABLE companies 
ADD KEY IF NOT EXISTS idx_customs_code (customs_code);

-- ============================================================================
-- ADD TEST CUSTOMS CODES FOR EXISTING COMPANIES
-- ============================================================================

-- Add test customs codes to existing companies for FTP connectivity testing
-- These are sample codes that can be used for testing the FTP system

-- Update Master System company
UPDATE companies 
SET customs_code = 'MASTER001',
    ftp_folder_path = '/invoices/MASTER001/'
WHERE company_code = 'MASTER' 
AND (customs_code IS NULL OR customs_code = '');

-- Update Demo Company
UPDATE companies 
SET customs_code = 'DEMO123456',
    ftp_folder_path = '/invoices/DEMO123456/'
WHERE company_code = 'DEMO001' 
AND (customs_code IS NULL OR customs_code = '');

-- Update Sample Clearing Company
UPDATE companies 
SET customs_code = 'SAMPLE789',
    ftp_folder_path = '/invoices/SAMPLE789/'
WHERE company_code = 'SAMPLE01' 
AND (customs_code IS NULL OR customs_code = '');

-- Add additional test companies with customs codes if they don't exist
INSERT IGNORE INTO companies (company_name, company_code, customs_code, address, telephone, email, ftp_folder_path, status) VALUES
('Test Import Co', 'TEST001', 'IMPORT2024', '789 Import Street, Port City, 9012', '+27-31-555-0001', '<EMAIL>', '/invoices/IMPORT2024/', 'active'),
('Export Solutions Ltd', 'EXPORT01', 'EXPORT2024', '321 Export Avenue, Trade Town, 3456', '+27-41-555-0002', '<EMAIL>', '/invoices/EXPORT2024/', 'active'),
('Global Freight Services', 'GLOBAL01', 'FREIGHT2024', '654 Freight Road, Logistics City, 7890', '+27-51-555-0003', '<EMAIL>', '/invoices/FREIGHT2024/', 'active');

-- ============================================================================
-- ENSURE CUSTOMS CODES ARE PROPERLY FORMATTED
-- ============================================================================

-- Ensure all companies have customs codes for FTP connectivity
-- Generate codes for any companies that still don't have them
UPDATE companies 
SET customs_code = CONCAT(UPPER(company_code), '2024')
WHERE (customs_code IS NULL OR customs_code = '') 
AND status = 'active';

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================
-- Run these queries to verify the migration was successful:

-- Check that all active companies have customs codes
-- SELECT COUNT(*) as companies_without_customs_codes FROM companies WHERE (customs_code IS NULL OR customs_code = '') AND status = 'active';

-- View all companies with their customs codes
-- SELECT id, company_name, company_code, customs_code, ftp_folder_path, status 
-- FROM companies 
-- ORDER BY company_name;

-- Check for duplicate customs codes (should return 0 rows)
-- SELECT customs_code, COUNT(*) as count 
-- FROM companies 
-- WHERE customs_code IS NOT NULL AND customs_code != ''
-- GROUP BY customs_code 
-- HAVING COUNT(*) > 1;

-- View users with their company customs codes
-- SELECT u.id, u.name, u.email, u.role, c.company_name, c.customs_code, c.ftp_folder_path
-- FROM users u 
-- LEFT JOIN companies c ON u.company_id = c.id 
-- WHERE u.status = 'active'
-- ORDER BY c.company_name, u.name;

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

-- Sample Customs Codes Created:
-- MASTER001 - Master System
-- DEMO123456 - Demo Company Ltd  
-- SAMPLE789 - Sample Clearing Co
-- IMPORT2024 - Test Import Co
-- EXPORT2024 - Export Solutions Ltd
-- FREIGHT2024 - Global Freight Services

-- These customs codes can be used for testing FTP connectivity.
-- Each company's users will access FTP folders based on their company's customs code.
-- For example: /invoices/DEMO123456/ for Demo Company users
