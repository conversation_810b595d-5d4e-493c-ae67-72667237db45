<?php
/**
 * Company Management
 * Master Admin interface for managing all companies
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireAdminAccess('master');

$database = new Database();
$db = $database->getConnection();

$error_message = '';
$success_message = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCsrfToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        $company_id = $_POST['company_id'] ?? '';
        
        switch ($action) {
            case 'activate':
                $query = "UPDATE companies SET status = 'active' WHERE id = ?";
                $stmt = $db->prepare($query);
                if ($stmt->execute([$company_id])) {
                    $success_message = 'Company activated successfully.';
                    logActivity('company_updated', "Company ID {$company_id} activated", $_SESSION['user_id']);
                } else {
                    $error_message = 'Failed to activate company.';
                }
                break;
                
            case 'suspend':
                $query = "UPDATE companies SET status = 'suspended' WHERE id = ?";
                $stmt = $db->prepare($query);
                if ($stmt->execute([$company_id])) {
                    $success_message = 'Company suspended successfully.';
                    logActivity('company_updated', "Company ID {$company_id} suspended", $_SESSION['user_id']);
                } else {
                    $error_message = 'Failed to suspend company.';
                }
                break;
        }
    }
}

// Get all companies with user counts
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';

$query = "SELECT c.*, 
                 COUNT(u.id) as user_count,
                 SUM(CASE WHEN u.status = 'active' THEN 1 ELSE 0 END) as active_users
          FROM companies c 
          LEFT JOIN users u ON c.id = u.company_id 
          WHERE 1=1";

$params = [];

if ($search) {
    $query .= " AND (c.company_name LIKE ? OR c.company_code LIKE ? OR c.email LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($status_filter) {
    $query .= " AND c.status = ?";
    $params[] = $status_filter;
}

$query .= " GROUP BY c.id ORDER BY c.created_at DESC";

$stmt = $db->prepare($query);
$stmt->execute($params);
$companies = $stmt->fetchAll();

// Get users for each company with their module permissions
$company_users = [];
$user_modules = [];
if (!empty($companies)) {
    $company_ids = array_column($companies, 'id');
    $placeholders = str_repeat('?,', count($company_ids) - 1) . '?';

    $users_query = "SELECT u.id, u.name, u.email, u.role, u.status, u.company_id, u.last_login, u.created_at
                    FROM users u
                    WHERE u.company_id IN ($placeholders) AND u.status != 'deleted'
                    ORDER BY u.company_id, u.name";

    $users_stmt = $db->prepare($users_query);
    $users_stmt->execute($company_ids);
    $all_users = $users_stmt->fetchAll();

    // Get user module permissions
    if (!empty($all_users)) {
        $user_ids = array_column($all_users, 'id');
        $user_placeholders = str_repeat('?,', count($user_ids) - 1) . '?';

        $modules_query = "SELECT user_id, module_name FROM user_rights WHERE user_id IN ($user_placeholders)";
        $modules_stmt = $db->prepare($modules_query);
        $modules_stmt->execute($user_ids);
        $all_modules = $modules_stmt->fetchAll();

        // Group modules by user
        foreach ($all_modules as $module) {
            $user_modules[$module['user_id']][] = $module['module_name'];
        }
    }

    // Group users by company
    foreach ($all_users as $user) {
        $company_users[$user['company_id']][] = $user;
    }
}

// Available modules for display
$available_modules = [
    'invoices' => 'Commercial Invoices',
    'freight' => 'Freight Management',
    'tariff' => 'Customs Tariff',
    'accounting' => 'Trade Finance',
    'backoffice' => 'Back Office'
];

$csrf_token = generateCsrfToken();
$page_title = "Company Management";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('master'); ?>
    <style>
        .search-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
        }
        
        .filter-select {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
        }
        
        .companies-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
        }
        
        .companies-table th {
            background: #2B5E5F;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }
        
        .companies-table td {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .companies-table tr:hover {
            background: #f8fafa;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-active {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-suspended {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'master', 'companies.php'); ?>
    
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Company Management</h1>
            <p class="page-subtitle">Manage all companies in the system</p>
        </div>
        
        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <div class="admin-card">
            <!-- Search and Filters -->
            <form method="GET" class="search-filters">
                <input type="text" name="search" class="search-input" 
                       placeholder="Search companies by name, code, or email..." 
                       value="<?php echo htmlspecialchars($search); ?>">
                
                <select name="status" class="filter-select">
                    <option value="">All Statuses</option>
                    <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                    <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                </select>
                
                <button type="submit" class="btn">Search</button>
                <a href="companies.php" class="btn btn-secondary">Clear</a>
                <a href="company_registration.php" class="btn btn-success">Add Company</a>
            </form>
            
            <!-- Companies Table -->
            <table class="companies-table">
                <thead>
                    <tr>
                        <th>Company</th>
                        <th>Code</th>
                        <th>Contact</th>
                        <th>Users</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($companies as $company): ?>
                        <tr>
                            <td>
                                <div style="font-weight: 600;"><?php echo htmlspecialchars($company['company_name']); ?></div>
                                <div style="font-size: 0.85rem; color: #666;"><?php echo htmlspecialchars($company['email']); ?></div>
                            </td>
                            <td>
                                <code style="background: #f0f0f0; padding: 0.25rem 0.5rem; border-radius: 4px;">
                                    <?php echo htmlspecialchars($company['company_code']); ?>
                                </code>
                            </td>
                            <td>
                                <?php if ($company['telephone']): ?>
                                    <div><?php echo htmlspecialchars($company['telephone']); ?></div>
                                <?php endif; ?>
                                <?php if ($company['website']): ?>
                                <div style="font-size: 0.85rem; color: #666;">
                                    <a href="<?php echo htmlspecialchars($company['website']); ?>" target="_blank">
                                        <?php echo htmlspecialchars($company['website']); ?>
                                    </a>
                                </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div><?php echo $company['active_users']; ?> active</div>
                                <div style="font-size: 0.85rem; color: #666;"><?php echo $company['user_count']; ?> total</div>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $company['status']; ?>">
                                    <?php echo ucfirst($company['status']); ?>
                                </span>
                            </td>
                            <td>
                                <?php echo date('M j, Y', strtotime($company['created_at'])); ?>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="company_edit.php?id=<?php echo $company['id']; ?>" class="btn btn-sm">Edit</a>

                                    <button class="btn btn-sm users-toggle-btn" onclick="toggleUsersRows(<?php echo $company['id']; ?>)">
                                        <span class="users-toggle-text">Users</span>
                                        <svg class="users-toggle-arrow" width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                                            <path d="M6 8L2 4h8l-4 4z"/>
                                        </svg>
                                    </button>

                                    <?php if ($company['status'] === 'active'): ?>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to suspend this company?')">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                            <input type="hidden" name="action" value="suspend">
                                            <input type="hidden" name="company_id" value="<?php echo $company['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-warning">Suspend</button>
                                        </form>
                                    <?php else: ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                            <input type="hidden" name="action" value="activate">
                                            <input type="hidden" name="company_id" value="<?php echo $company['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-success">Activate</button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>

                        <!-- User rows for this company (initially hidden) -->
                        <?php if (isset($company_users[$company['id']]) && !empty($company_users[$company['id']])): ?>
                            <?php foreach ($company_users[$company['id']] as $user): ?>
                                <tr class="user-row user-row-<?php echo $company['id']; ?>" style="display: none;">
                                    <td colspan="2" class="user-info-cell">
                                        <div class="user-info-container">
                                            <div class="user-name"><?php echo htmlspecialchars($user['name']); ?></div>
                                            <div class="user-email"><?php echo htmlspecialchars($user['email']); ?></div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="user-role role-<?php echo $user['role']; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $user['role'])); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="user-status status-<?php echo $user['status']; ?>">
                                            <?php echo ucfirst($user['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="module-access">
                                            <?php
                                            $user_module_list = $user_modules[$user['id']] ?? [];
                                            foreach ($available_modules as $module_key => $module_name):
                                                $has_access = in_array($module_key, $user_module_list);
                                            ?>
                                                <div class="module-item">
                                                    <label class="module-checkbox">
                                                        <input type="checkbox"
                                                               data-user-id="<?php echo $user['id']; ?>"
                                                               data-module="<?php echo $module_key; ?>"
                                                               <?php echo $has_access ? 'checked' : ''; ?>
                                                               onchange="updateModuleAccess(this)">
                                                        <span class="checkmark"></span>
                                                        <span class="module-label"><?php echo htmlspecialchars($module_name); ?></span>
                                                    </label>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="user-actions">
                                            <a href="user_edit.php?id=<?php echo $user['id']; ?>" class="btn btn-xs">Edit User</a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr class="user-row user-row-<?php echo $company['id']; ?> no-users-row" style="display: none;">
                                <td colspan="6" class="no-users-cell">
                                    <div class="no-users-message">No users found for this company</div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <?php if (empty($companies)): ?>
                <div style="text-align: center; padding: 3rem; color: #666;">
                    <p>No companies found.</p>
                    <a href="company_registration.php" class="btn" style="margin-top: 1rem;">Register First Company</a>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <style>
        /* Improved Action Buttons Layout */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .action-buttons .btn {
            white-space: nowrap;
        }

        /* Users Toggle Button */
        .users-toggle-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            color: #374151;
            transition: all 0.2s ease;
        }

        .users-toggle-btn:hover {
            background: #f1f5f9;
            border-color: #2B5E5F;
            color: #2B5E5F;
        }

        .users-toggle-arrow {
            transition: transform 0.2s ease;
            color: currentColor;
        }

        .users-toggle-btn.active {
            background: #2B5E5F;
            border-color: #2B5E5F;
            color: white;
        }

        .users-toggle-btn.active .users-toggle-arrow {
            transform: rotate(180deg);
        }

        /* Integrated User Rows */
        .user-row {
            background: #f8fafc !important;
            border-left: 3px solid #2B5E5F;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(-10px);
        }

        .user-row:hover {
            background: #f1f5f9 !important;
            transform: translateY(0) scale(1.01);
        }

        .user-row td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e5e7eb;
            vertical-align: top;
            font-size: 0.9rem;
        }

        .user-info-cell {
            padding-left: 2rem !important;
            position: relative;
        }

        .user-info-cell::before {
            content: '└─';
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #2B5E5F;
            font-weight: bold;
            font-size: 0.8rem;
        }

        .user-info-container {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .user-name {
            font-weight: 600;
            color: #374151;
            font-size: 0.85rem;
        }

        .user-email {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .user-role, .user-status {
            font-size: 0.7rem;
            padding: 0.125rem 0.5rem;
            border-radius: 12px;
            font-weight: 500;
            white-space: nowrap;
        }

        .role-master_admin {
            background: #fef3c7;
            color: #92400e;
        }

        .role-company_admin {
            background: #dbeafe;
            color: #1e40af;
        }

        .role-user {
            background: #f3f4f6;
            color: #374151;
        }

        .status-active {
            background: #d1fae5;
            color: #065f46;
        }

        .status-inactive {
            background: #f3f4f6;
            color: #6b7280;
        }

        .status-suspended {
            background: #fee2e2;
            color: #991b1b;
        }

        /* No Users Row */
        .no-users-row {
            background: #f9fafb !important;
            border-left: 3px solid #d1d5db;
        }

        .no-users-cell {
            padding: 1.5rem 2rem !important;
            text-align: center;
            font-style: italic;
            color: #6b7280;
        }

        .no-users-message {
            font-size: 0.9rem;
        }

        /* Module Access Styling */
        .module-access {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            min-width: 200px;
        }

        .module-item {
            display: flex;
            align-items: center;
        }

        .module-checkbox {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 0.75rem;
            gap: 0.5rem;
        }

        .module-checkbox input[type="checkbox"] {
            display: none;
        }

        .checkmark {
            width: 14px;
            height: 14px;
            border: 1px solid #d1d5db;
            border-radius: 3px;
            position: relative;
            transition: all 0.2s ease;
            flex-shrink: 0;
        }

        .module-checkbox input[type="checkbox"]:checked + .checkmark {
            background: #2B5E5F;
            border-color: #2B5E5F;
        }

        .module-checkbox input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 10px;
            font-weight: bold;
        }

        .module-label {
            color: #374151;
            white-space: nowrap;
        }

        /* User Actions */
        .user-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .btn-xs {
            padding: 0.375rem 0.75rem;
            font-size: 0.75rem;
            border-radius: 4px;
            background: #2B5E5F;
            color: white;
            border: 1px solid #2B5E5F;
            transition: all 0.2s ease;
        }

        .btn-xs:hover {
            background: #1e4a4b;
            border-color: #1e4a4b;
            transform: translateY(-1px);
        }

        /* Animation for smooth table expansion */
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .user-row.show {
            animation: slideDown 0.3s ease forwards;
        }
    </style>

    <script>
        function toggleUsersRows(companyId) {
            // Get all user rows for this company
            const userRows = document.querySelectorAll(`.user-row-${companyId}`);
            const toggleBtn = event.target.closest('.users-toggle-btn');

            // Close all other company user rows
            const allUserRows = document.querySelectorAll('.user-row');
            const allToggleBtns = document.querySelectorAll('.users-toggle-btn');

            allUserRows.forEach(row => {
                if (!row.classList.contains(`user-row-${companyId}`)) {
                    row.style.display = 'none';
                }
            });

            allToggleBtns.forEach(btn => {
                if (btn !== toggleBtn) {
                    btn.classList.remove('active');
                }
            });

            // Toggle current company's user rows
            const isCurrentlyVisible = userRows.length > 0 && userRows[0].style.display !== 'none';

            if (isCurrentlyVisible) {
                // Hide user rows
                userRows.forEach(row => {
                    row.style.display = 'none';
                });
                toggleBtn.classList.remove('active');
            } else {
                // Show user rows with smooth animation
                userRows.forEach((row, index) => {
                    setTimeout(() => {
                        row.style.display = 'table-row';
                        row.style.opacity = '0';
                        row.style.transform = 'translateY(-10px)';

                        // Trigger animation
                        setTimeout(() => {
                            row.style.transition = 'all 0.3s ease';
                            row.style.opacity = '1';
                            row.style.transform = 'translateY(0)';
                        }, 10);
                    }, index * 50); // Stagger the animation
                });
                toggleBtn.classList.add('active');
            }
        }

        // Update module access via AJAX
        function updateModuleAccess(checkbox) {
            const userId = checkbox.dataset.userId;
            const module = checkbox.dataset.module;
            const hasAccess = checkbox.checked;

            // Show loading state
            checkbox.disabled = true;

            fetch('update_module_access.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    user_id: userId,
                    module: module,
                    has_access: hasAccess,
                    csrf_token: '<?php echo $csrf_token; ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success feedback
                    showNotification('Module access updated successfully', 'success');
                } else {
                    // Revert checkbox state
                    checkbox.checked = !hasAccess;
                    showNotification(data.message || 'Failed to update module access', 'error');
                }
            })
            .catch(error => {
                // Revert checkbox state
                checkbox.checked = !hasAccess;
                showNotification('Network error occurred', 'error');
                console.error('Error:', error);
            })
            .finally(() => {
                checkbox.disabled = false;
            });
        }

        // Show notification function
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                transition: all 0.3s ease;
                ${type === 'success' ? 'background: #10b981;' : 'background: #ef4444;'}
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
    </script>
</body>
</html>
