<?php
/**
 * User Class
 * Handles user authentication and management
 */

require_once __DIR__ . '/../config/config.php';

class User {
    private $conn;
    private $table_name = "users";
    
    public $id;
    public $name;
    public $email;
    public $password;
    public $role;
    public $company_id;
    public $ftp_identification_code;
    public $status;
    public $created_by;
    
    public function __construct($db) {
        $this->conn = $db;
    }
    
    /**
     * Authenticate user login with company code
     */
    public function loginWithCompanyCode($email, $password, $company_code) {
        $query = "SELECT u.id, u.name, u.email, u.password, u.role, u.company_id, u.status,
                         c.company_name, c.company_code, c.customs_code, c.ftp_folder_path
                  FROM " . $this->table_name . " u
                  LEFT JOIN companies c ON u.company_id = c.id
                  WHERE u.email = :email AND c.company_code = :company_code
                  AND u.status = 'active' AND c.status = 'active'";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':company_code', $company_code);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            if (password_verify($password, $row['password'])) {
                // Update last login
                $this->updateLastLogin($row['id']);

                // Set session variables
                $_SESSION['user_id'] = $row['id'];
                $_SESSION['user_name'] = $row['name'];
                $_SESSION['user_email'] = $row['email'];
                $_SESSION['role'] = $row['role'];
                $_SESSION['company_id'] = $row['company_id'];
                $_SESSION['company_name'] = $row['company_name'];
                $_SESSION['company_code'] = $row['company_code'];
                $_SESSION['customs_code'] = $row['customs_code'];
                $_SESSION['ftp_folder_path'] = $row['ftp_folder_path'];
                $_SESSION['last_activity'] = time();

                // Set role flags for compatibility with existing system
                $_SESSION['is_admin'] = ($row['role'] === 'company_admin');
                $_SESSION['is_master_admin'] = ($row['role'] === 'master_admin');
                $_SESSION['user_role'] = $row['role'];

                return true;
            }
        }

        return false;
    }

    /**
     * Legacy login method for backward compatibility
     */
    public function login($email, $password) {
        // For backward compatibility, try to login without company code
        // This will be used for master admin or fallback scenarios
        $query = "SELECT u.id, u.name, u.email, u.password, u.role, u.company_id, u.status,
                         c.company_name, c.company_code, c.customs_code, c.ftp_folder_path
                  FROM " . $this->table_name . " u
                  LEFT JOIN companies c ON u.company_id = c.id
                  WHERE u.email = :email AND u.status = 'active'";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            if (password_verify($password, $row['password'])) {
                // Update last login
                $this->updateLastLogin($row['id']);

                // Set session variables
                $_SESSION['user_id'] = $row['id'];
                $_SESSION['user_name'] = $row['name'];
                $_SESSION['user_email'] = $row['email'];
                $_SESSION['role'] = $row['role'];
                $_SESSION['company_id'] = $row['company_id'];
                $_SESSION['company_name'] = $row['company_name'];
                $_SESSION['company_code'] = $row['company_code'];
                $_SESSION['customs_code'] = $row['customs_code'];
                $_SESSION['ftp_folder_path'] = $row['ftp_folder_path'];
                $_SESSION['last_activity'] = time();

                // Set role flags for compatibility
                $_SESSION['is_admin'] = ($row['role'] === 'company_admin');
                $_SESSION['is_master_admin'] = ($row['role'] === 'master_admin');
                $_SESSION['user_role'] = $row['role'];

                return true;
            }
        }

        return false;
    }



    /**
     * Create new user
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  SET name = :name, email = :email, password = :password,
                      role = :role, company_id = :company_id,
                      status = :status, created_by = :created_by";

        $stmt = $this->conn->prepare($query);

        // Hash password
        $hashed_password = password_hash($this->password, PASSWORD_DEFAULT);

        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':email', $this->email);
        $stmt->bindParam(':password', $hashed_password);
        $stmt->bindParam(':role', $this->role);
        $stmt->bindParam(':company_id', $this->company_id);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':created_by', $this->created_by);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }
    
    /**
     * Get user by ID
     */
    public function getById($id) {
        $query = "SELECT u.*, c.company_name, c.company_code
                  FROM " . $this->table_name . " u
                  LEFT JOIN companies c ON u.company_id = c.id
                  WHERE u.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }

        return false;
    }
    
    /**
     * Get all users (admin only)
     */
    public function getAll() {
        $query = "SELECT u.*, c.company_name, c.company_code
                  FROM " . $this->table_name . " u
                  LEFT JOIN companies c ON u.company_id = c.id
                  ORDER BY u.created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Update user
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET name = :name, email = :email, role = :role,
                      company_id = :company_id, status = :status
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':email', $this->email);
        $stmt->bindParam(':role', $this->role);
        $stmt->bindParam(':company_id', $this->company_id);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }
    
    /**
     * Delete user
     */
    public function delete($id) {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        
        return $stmt->execute();
    }
    
    /**
     * Update last login timestamp
     */
    private function updateLastLogin($user_id) {
        $query = "UPDATE " . $this->table_name . " SET last_login = NOW() WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();
    }
    
    /**
     * Check if email exists
     */
    public function emailExists($email, $exclude_id = null) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE email = :email";
        
        if ($exclude_id) {
            $query .= " AND id != :exclude_id";
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':email', $email);
        
        if ($exclude_id) {
            $stmt->bindParam(':exclude_id', $exclude_id);
        }
        
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }
    
    /**
     * Get user rights
     */
    public function getUserRights($user_id) {
        $query = "SELECT module_name FROM user_rights WHERE user_id = :user_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        
        $rights = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $rights[] = $row['module_name'];
        }
        
        return $rights;
    }
    
    /**
     * Set user rights
     */
    public function setUserRights($user_id, $modules) {
        // First, delete existing rights
        $delete_query = "DELETE FROM user_rights WHERE user_id = :user_id";
        $delete_stmt = $this->conn->prepare($delete_query);
        $delete_stmt->bindParam(':user_id', $user_id);
        $delete_stmt->execute();
        
        // Then, insert new rights
        if (!empty($modules)) {
            $insert_query = "INSERT INTO user_rights (user_id, module_name) VALUES (:user_id, :module_name)";
            $insert_stmt = $this->conn->prepare($insert_query);
            
            foreach ($modules as $module) {
                $insert_stmt->bindParam(':user_id', $user_id);
                $insert_stmt->bindParam(':module_name', $module);
                $insert_stmt->execute();
            }
        }
        
        return true;
    }
    
    /**
     * Logout user
     */
    public static function logout() {
        session_unset();
        session_destroy();
        header('Location: ' . BASE_URL . 'login.php');
        exit();
    }
}
?>
