<?php
/**
 * <PERSON><PERSON> Browser for Master Admin
 * Browse and manage user-specific FTP folders using identification codes
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../classes/FTPManager.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireAdminAccess('master');

$database = new Database();
$db = $database->getConnection();

$error_message = '';
$success_message = '';
$current_user_id = null;
$current_ftp_code = null;
$ftp_files = [];
$breadcrumb = [];

// Handle FTP operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'browse_user_ftp':
                $user_id = intval($_POST['user_id'] ?? 0);
                if ($user_id > 0) {
                    header("Location: ftp_browser.php?user_id=" . $user_id);
                    exit();
                }
                break;
                
            case 'download_file':
                $user_id = intval($_POST['user_id'] ?? 0);
                $file_path = $_POST['file_path'] ?? '';
                
                if ($user_id > 0 && !empty($file_path)) {
                    try {
                        // Get user's FTP credentials from assignments
                        $ftp_credentials = getUserFTPCredentials($db, $user_id);
                        if ($ftp_credentials) {
                            $ftp_manager = new FTPManager($ftp_credentials['username'], $ftp_credentials['password']);
                            
                            // Create temporary file for download
                            $temp_file = tempnam(sys_get_temp_dir(), 'ftp_download_');
                            
                            if ($ftp_manager->downloadFile($file_path, $temp_file)) {
                                // Send file to browser
                                $filename = basename($file_path);
                                header('Content-Type: application/octet-stream');
                                header('Content-Disposition: attachment; filename="' . $filename . '"');
                                header('Content-Length: ' . filesize($temp_file));
                                readfile($temp_file);
                                unlink($temp_file);
                                exit();
                            } else {
                                $error_message = 'Failed to download file from FTP server.';
                            }
                        } else {
                            $error_message = 'FTP credentials not found for this user.';
                        }
                    } catch (Exception $e) {
                        $error_message = 'Error downloading file: ' . $e->getMessage();
                    }
                }
                break;
        }
    }
}

// Get user ID from URL parameter
if (isset($_GET['user_id'])) {
    $current_user_id = intval($_GET['user_id']);
    
    // Get user details and company customs code
    $query = "SELECT u.id, u.name, u.email, c.company_name, c.company_code, c.customs_code
              FROM users u
              LEFT JOIN companies c ON u.company_id = c.id
              WHERE u.id = ? AND u.status = 'active'";
    $stmt = $db->prepare($query);
    $stmt->execute([$current_user_id]);
    $user_details = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user_details) {
        $current_customs_code = $user_details['customs_code'];
        
        // Try to get FTP files for this user
        try {
            $ftp_credentials = getUserFTPCredentials($db, $current_user_id);
            if ($ftp_credentials) {
                $ftp_manager = new FTPManager($ftp_credentials['username'], $ftp_credentials['password']);
                
                // Build user's FTP folder path using their company's customs code
                $user_ftp_path = '/invoices/' . $current_customs_code . '/';
                $ftp_files = $ftp_manager->getClientFiles($user_ftp_path);

                $breadcrumb = [
                    ['name' => 'FTP Root', 'path' => '/'],
                    ['name' => 'Invoices', 'path' => '/invoices/'],
                    ['name' => $current_customs_code, 'path' => $user_ftp_path]
                ];
            } else {
                $error_message = 'No FTP credentials configured for this user.';
            }
        } catch (Exception $e) {
            $error_message = 'Error connecting to FTP: ' . $e->getMessage();
        }
    } else {
        $error_message = 'User not found.';
    }
}

// Get all users with their company customs codes for the user selector
$query = "SELECT u.id, u.name, u.email, c.company_name, c.company_code, c.customs_code,
                 COUNT(DISTINCT cfa.id) as ftp_assignments
          FROM users u
          LEFT JOIN companies c ON u.company_id = c.id
          LEFT JOIN company_ftp_assignments cfa ON c.id = cfa.company_id AND cfa.is_active = TRUE
          WHERE u.status = 'active' AND u.role != 'master_admin'
          GROUP BY u.id
          ORDER BY c.company_name, u.name";
$stmt = $db->prepare($query);
$stmt->execute();
$all_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

/**
 * Get FTP credentials for a user based on their company's FTP assignments
 */
function getUserFTPCredentials($db, $user_id) {
    $query = "SELECT fs.host, fs.port, fs.username, fs.password_encrypted, fs.base_path, c.company_id
              FROM users u
              JOIN companies c ON u.company_id = c.id
              JOIN company_ftp_assignments cfa ON c.id = cfa.company_id AND cfa.is_active = TRUE
              JOIN ftp_servers fs ON cfa.ftp_server_id = fs.id AND fs.is_active = TRUE
              WHERE u.id = ?
              LIMIT 1";
    
    $stmt = $db->prepare($query);
    $stmt->execute([$user_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        // Decrypt password
        $encryption_key = 'ftp_encryption_key_2024'; // Should match the key used in ftp_management.php
        $decrypted_password = decryptFTPPassword($result['password_encrypted'], $encryption_key);
        
        return [
            'host' => $result['host'],
            'port' => $result['port'],
            'username' => $result['username'],
            'password' => $decrypted_password,
            'base_path' => $result['base_path']
        ];
    }
    
    return null;
}

/**
 * Decrypt FTP password
 */
function decryptFTPPassword($encrypted_password, $encryption_key) {
    $iv = substr(hash('sha256', $encryption_key), 0, 16);
    return openssl_decrypt(base64_decode($encrypted_password), 'AES-256-CBC', $encryption_key, 0, $iv);
}

// Generate CSRF token
$csrf_token = generateCSRFToken();

// Generate admin header
echo generateAdminHeader('master', 'FTP Browser');
?>

<div class="admin-container">
    <div class="admin-header">
        <h1>FTP Browser & File Management</h1>
        <p>Browse and manage user-specific FTP folders using their unique identification codes</p>
    </div>

    <?php if ($error_message): ?>
        <div class="alert alert-error">
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>

    <?php if ($success_message): ?>
        <div class="alert alert-success">
            <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>

    <!-- User Selection -->
    <div class="admin-card">
        <h2 style="color: #2B5E5F; margin-bottom: 1.5rem;">Select User to Browse FTP</h2>
        
        <form method="POST" style="margin-bottom: 2rem;">
            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
            <input type="hidden" name="action" value="browse_user_ftp">
            
            <div style="display: flex; gap: 1rem; align-items: end;">
                <div style="flex: 1;">
                    <label for="user_id" style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Select User:</label>
                    <select name="user_id" id="user_id" required style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px;">
                        <option value="">Choose a user...</option>
                        <?php foreach ($all_users as $user): ?>
                            <option value="<?php echo $user['id']; ?>" <?php echo ($current_user_id == $user['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($user['name']); ?>
                                (<?php echo htmlspecialchars($user['customs_code'] ?? 'No Code'); ?>)
                                - <?php echo htmlspecialchars($user['company_name']); ?>
                                <?php if ($user['ftp_assignments'] == 0): ?>
                                    <em style="color: #dc2626;"> - No FTP Access</em>
                                <?php endif; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <button type="submit" class="btn-primary">Browse FTP</button>
            </div>
        </form>
    </div>

    <?php if ($current_user_id && $user_details): ?>
        <!-- User FTP Details -->
        <div class="admin-card">
            <h2 style="color: #2B5E5F; margin-bottom: 1rem;">FTP Access for <?php echo htmlspecialchars($user_details['name']); ?></h2>
            
            <div style="background: #f8fafc; padding: 1rem; border-radius: 8px; margin-bottom: 1.5rem;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div>
                        <strong>User:</strong> <?php echo htmlspecialchars($user_details['name']); ?><br>
                        <strong>Email:</strong> <?php echo htmlspecialchars($user_details['email']); ?>
                    </div>
                    <div>
                        <strong>Company:</strong> <?php echo htmlspecialchars($user_details['company_name']); ?><br>
                        <strong>Company Code:</strong> <?php echo htmlspecialchars($user_details['company_code']); ?>
                    </div>
                    <div>
                        <strong>Customs Code:</strong>
                        <span style="background: #2B5E5F; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-family: monospace;">
                            <?php echo htmlspecialchars($current_customs_code ?? 'Not Assigned'); ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Breadcrumb -->
            <?php if (!empty($breadcrumb)): ?>
                <div style="margin-bottom: 1rem;">
                    <nav style="font-size: 0.9rem; color: #666;">
                        <?php foreach ($breadcrumb as $index => $crumb): ?>
                            <?php if ($index > 0): ?> / <?php endif; ?>
                            <span style="<?php echo $index === count($breadcrumb) - 1 ? 'font-weight: 600; color: #2B5E5F;' : ''; ?>">
                                <?php echo htmlspecialchars($crumb['name']); ?>
                            </span>
                        <?php endforeach; ?>
                    </nav>
                </div>
            <?php endif; ?>

            <!-- File List -->
            <?php if (!empty($ftp_files)): ?>
                <div style="overflow-x: auto;">
                    <table class="companies-table">
                        <thead>
                            <tr>
                                <th>File Name</th>
                                <th>Size</th>
                                <th>Modified</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($ftp_files as $file): ?>
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                                            <span style="font-size: 1.2rem;">📄</span>
                                            <span style="font-weight: 600;"><?php echo htmlspecialchars($file['filename']); ?></span>
                                        </div>
                                    </td>
                                    <td><?php echo formatFileSize($file['size']); ?></td>
                                    <td><?php echo date('Y-m-d H:i:s', $file['modified']); ?></td>
                                    <td>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                            <input type="hidden" name="action" value="download_file">
                                            <input type="hidden" name="user_id" value="<?php echo $current_user_id; ?>">
                                            <input type="hidden" name="file_path" value="<?php echo htmlspecialchars($file['full_path']); ?>">
                                            <button type="submit" class="btn-primary btn-sm">Download</button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div style="text-align: center; padding: 2rem; color: #666;">
                    <div style="font-size: 3rem; margin-bottom: 1rem;">📁</div>
                    <h3>No Files Found</h3>
                    <p>This user's FTP folder is empty or could not be accessed.</p>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<?php
/**
 * Format file size in human readable format
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

echo generateAdminFooter();
?>
