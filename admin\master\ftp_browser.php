<?php
/**
 * <PERSON><PERSON> Browser for Master Admin
 * Browse and manage user-specific FTP folders using identification codes
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../classes/FTPManager.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireAdminAccess('master');

$database = new Database();
$db = $database->getConnection();

$error_message = '';
$success_message = '';
$current_user_id = null;
$current_ftp_code = null;
$ftp_files = [];
$breadcrumb = [];

// Handle FTP operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'browse_user_ftp':
                $user_id = intval($_POST['user_id'] ?? 0);
                if ($user_id > 0) {
                    header("Location: ftp_browser.php?user_id=" . $user_id);
                    exit();
                }
                break;
                
            case 'download_file':
                $user_id = intval($_POST['user_id'] ?? 0);
                $file_path = $_POST['file_path'] ?? '';
                
                if ($user_id > 0 && !empty($file_path)) {
                    try {
                        // Get user's FTP credentials from assignments
                        $ftp_credentials = getUserFTPCredentials($db, $user_id);
                        if ($ftp_credentials) {
                            $ftp_manager = new FTPManager($ftp_credentials['username'], $ftp_credentials['password']);
                            
                            // Create temporary file for download
                            $temp_file = tempnam(sys_get_temp_dir(), 'ftp_download_');
                            
                            if ($ftp_manager->downloadFile($file_path, $temp_file)) {
                                // Send file to browser
                                $filename = basename($file_path);
                                header('Content-Type: application/octet-stream');
                                header('Content-Disposition: attachment; filename="' . $filename . '"');
                                header('Content-Length: ' . filesize($temp_file));
                                readfile($temp_file);
                                unlink($temp_file);
                                exit();
                            } else {
                                $error_message = 'Failed to download file from FTP server.';
                            }
                        } else {
                            $error_message = 'FTP credentials not found for this user.';
                        }
                    } catch (Exception $e) {
                        $error_message = 'Error downloading file: ' . $e->getMessage();
                    }
                }
                break;
        }
    }
}

// Get user ID from URL parameter
if (isset($_GET['user_id'])) {
    $current_user_id = intval($_GET['user_id']);
    
    // Get user details and company customs code
    $query = "SELECT u.id, u.name, u.email, c.company_name, c.company_code, c.customs_code
              FROM users u
              LEFT JOIN companies c ON u.company_id = c.id
              WHERE u.id = ? AND u.status = 'active'";
    $stmt = $db->prepare($query);
    $stmt->execute([$current_user_id]);
    $user_details = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user_details) {
        $current_customs_code = $user_details['customs_code'];
        
        // Try to get FTP files for this user
        try {
            $ftp_credentials = getUserFTPCredentials($db, $current_user_id);
            if ($ftp_credentials) {
                $ftp_manager = new FTPManager($ftp_credentials['username'], $ftp_credentials['password']);
                
                // Build user's FTP folder path using their company's customs code
                $user_ftp_path = '/invoices/' . $current_customs_code . '/';
                $ftp_files = $ftp_manager->getClientFiles($user_ftp_path);

                $breadcrumb = [
                    ['name' => 'FTP Root', 'path' => '/'],
                    ['name' => 'Invoices', 'path' => '/invoices/'],
                    ['name' => $current_customs_code, 'path' => $user_ftp_path]
                ];
            } else {
                $error_message = 'No FTP credentials configured for this user.';
            }
        } catch (Exception $e) {
            $error_message = 'Error connecting to FTP: ' . $e->getMessage();
        }
    } else {
        $error_message = 'User not found.';
    }
}

// Get all users with their company customs codes for the user selector
$query = "SELECT u.id, u.name, u.email, c.company_name, c.company_code, c.customs_code,
                 COUNT(DISTINCT cfa.id) as ftp_assignments
          FROM users u
          LEFT JOIN companies c ON u.company_id = c.id
          LEFT JOIN company_ftp_assignments cfa ON c.id = cfa.company_id AND cfa.is_active = TRUE
          WHERE u.status = 'active' AND u.role != 'master_admin'
          GROUP BY u.id
          ORDER BY c.company_name, u.name";
$stmt = $db->prepare($query);
$stmt->execute();
$all_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

/**
 * Get FTP credentials for a user based on their company's FTP assignments
 */
function getUserFTPCredentials($db, $user_id) {
    $query = "SELECT fs.host, fs.port, fs.username, fs.password_encrypted, fs.base_path, c.company_id
              FROM users u
              JOIN companies c ON u.company_id = c.id
              JOIN company_ftp_assignments cfa ON c.id = cfa.company_id AND cfa.is_active = TRUE
              JOIN ftp_servers fs ON cfa.ftp_server_id = fs.id AND fs.is_active = TRUE
              WHERE u.id = ?
              LIMIT 1";
    
    $stmt = $db->prepare($query);
    $stmt->execute([$user_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        // Decrypt password
        $encryption_key = 'ftp_encryption_key_2024'; // Should match the key used in ftp_management.php
        $decrypted_password = decryptFTPPassword($result['password_encrypted'], $encryption_key);
        
        return [
            'host' => $result['host'],
            'port' => $result['port'],
            'username' => $result['username'],
            'password' => $decrypted_password,
            'base_path' => $result['base_path']
        ];
    }
    
    return null;
}

/**
 * Decrypt FTP password
 */
function decryptFTPPassword($encrypted_password, $encryption_key) {
    $iv = substr(hash('sha256', $encryption_key), 0, 16);
    return openssl_decrypt(base64_decode($encrypted_password), 'AES-256-CBC', $encryption_key, 0, $iv);
}

// Generate CSRF token
$csrf_token = generateCSRFToken();

$page_title = "FTP Browser & File Management";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('master'); ?>
    <style>
        .ftp-browser-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .ftp-header {
            background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.15);
        }

        .ftp-header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .ftp-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }

        .ftp-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
            overflow: hidden;
            border: 1px solid rgba(226, 232, 240, 0.8);
        }

        .ftp-card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 1.5rem 2rem;
            border-bottom: 1px solid rgba(226, 232, 240, 0.5);
        }

        .ftp-card-header h2 {
            color: #2B5E5F;
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .ftp-card-body {
            padding: 2rem;
        }

        .user-selector {
            display: flex;
            gap: 1rem;
            align-items: end;
            flex-wrap: wrap;
        }

        .form-group {
            flex: 1;
            min-width: 300px;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
        }

        .form-select {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
            transition: all 0.2s ease;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
        }

        .form-select:focus {
            outline: none;
            border-color: #2B5E5F;
            box-shadow: 0 0 0 3px rgba(43, 94, 95, 0.1);
        }

        .btn-browse {
            background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 100%);
            color: white;
            border: none;
            padding: 0.875rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            white-space: nowrap;
        }

        .btn-browse:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(43, 94, 95, 0.3);
        }

        .user-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            border: 1px solid rgba(226, 232, 240, 0.5);
        }

        .user-info-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .user-info-label {
            font-weight: 600;
            color: #6b7280;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .user-info-value {
            font-weight: 600;
            color: #1f2937;
            font-size: 1rem;
        }

        .customs-code {
            background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-weight: 700;
            letter-spacing: 1px;
            display: inline-block;
            box-shadow: 0 2px 4px rgba(43, 94, 95, 0.2);
        }

        .breadcrumb {
            background: #f8fafc;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            border: 1px solid #e5e7eb;
        }

        .breadcrumb-nav {
            font-size: 0.9rem;
            color: #6b7280;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .breadcrumb-current {
            font-weight: 600;
            color: #2B5E5F;
        }

        .files-table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }

        .files-table {
            width: 100%;
            border-collapse: collapse;
        }

        .files-table thead {
            background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 100%);
            color: white;
        }

        .files-table th {
            padding: 1rem 1.5rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .files-table td {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        .files-table tbody tr:hover {
            background: #f8fafc;
        }

        .file-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .file-icon {
            font-size: 1.5rem;
            width: 2rem;
            text-align: center;
        }

        .file-name {
            font-weight: 600;
            color: #1f2937;
        }

        .file-size {
            color: #6b7280;
            font-weight: 500;
        }

        .file-date {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .btn-download {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-download:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: #6b7280;
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #374151;
        }

        .empty-state p {
            font-size: 1rem;
            margin: 0;
        }

        .alert {
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            border: 1px solid;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .alert-error {
            background: #fef2f2;
            border-color: #fecaca;
            color: #991b1b;
        }

        .alert-success {
            background: #f0fdf4;
            border-color: #bbf7d0;
            color: #166534;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #2B5E5F;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .file-stats {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #e5e7eb;
        }

        .file-stats-item {
            text-align: center;
        }

        .file-stats-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: #2B5E5F;
            display: block;
        }

        .file-stats-label {
            font-size: 0.85rem;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        @media (max-width: 768px) {
            .ftp-browser-container {
                padding: 1rem;
            }

            .user-selector {
                flex-direction: column;
                align-items: stretch;
            }

            .form-group {
                min-width: auto;
            }

            .user-info-grid {
                grid-template-columns: 1fr;
            }

            .files-table-container {
                overflow-x: auto;
            }

            .files-table {
                min-width: 600px;
            }

            .file-stats {
                flex-direction: column;
                gap: 1rem;
            }

            .file-stats-item {
                display: flex;
                justify-content: space-between;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'master', 'ftp_browser.php'); ?>

    <main class="main-content">
        <div class="ftp-browser-container">
            <div class="ftp-header">
                <h1>
                    <span>🗂️</span>
                    FTP Browser & File Management
                </h1>
                <p>Browse and manage user-specific FTP folders using their unique identification codes</p>
            </div>

            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <span>⚠️</span>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <span>✅</span>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- User Selection -->
            <div class="ftp-card">
                <div class="ftp-card-header">
                    <h2>
                        <span>👤</span>
                        Select User to Browse FTP
                    </h2>
                </div>
                <div class="ftp-card-body">
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="action" value="browse_user_ftp">

                        <div class="user-selector">
                            <div class="form-group">
                                <label for="user_id" class="form-label">Select User:</label>
                                <select name="user_id" id="user_id" required class="form-select">
                                    <option value="">Choose a user...</option>
                                    <?php foreach ($all_users as $user): ?>
                                        <option value="<?php echo $user['id']; ?>" <?php echo ($current_user_id == $user['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($user['name']); ?>
                                            (<?php echo htmlspecialchars($user['customs_code'] ?? 'No Code'); ?>)
                                            - <?php echo htmlspecialchars($user['company_name']); ?>
                                            <?php if ($user['ftp_assignments'] == 0): ?>
                                                - No FTP Access
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <button type="submit" class="btn-browse">
                                <span>🔍</span>
                                Browse FTP
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <?php if ($current_user_id && $user_details): ?>
                <!-- User FTP Details -->
                <div class="ftp-card">
                    <div class="ftp-card-header">
                        <h2>
                            <span>📁</span>
                            FTP Access for <?php echo htmlspecialchars($user_details['name']); ?>
                        </h2>
                    </div>
                    <div class="ftp-card-body">
                        <div class="user-info-grid">
                            <div class="user-info-item">
                                <span class="user-info-label">User Name</span>
                                <span class="user-info-value"><?php echo htmlspecialchars($user_details['name']); ?></span>
                            </div>
                            <div class="user-info-item">
                                <span class="user-info-label">Email Address</span>
                                <span class="user-info-value"><?php echo htmlspecialchars($user_details['email']); ?></span>
                            </div>
                            <div class="user-info-item">
                                <span class="user-info-label">Company</span>
                                <span class="user-info-value"><?php echo htmlspecialchars($user_details['company_name']); ?></span>
                            </div>
                            <div class="user-info-item">
                                <span class="user-info-label">Company Code</span>
                                <span class="user-info-value"><?php echo htmlspecialchars($user_details['company_code']); ?></span>
                            </div>
                            <div class="user-info-item">
                                <span class="user-info-label">Customs Code</span>
                                <span class="customs-code">
                                    <?php echo htmlspecialchars($current_customs_code ?? 'Not Assigned'); ?>
                                </span>
                            </div>
                        </div>

                        <!-- Breadcrumb -->
                        <?php if (!empty($breadcrumb)): ?>
                            <div class="breadcrumb">
                                <nav class="breadcrumb-nav">
                                    <span>📍</span>
                                    <?php foreach ($breadcrumb as $index => $crumb): ?>
                                        <div class="breadcrumb-item">
                                            <?php if ($index > 0): ?>
                                                <span>→</span>
                                            <?php endif; ?>
                                            <span class="<?php echo $index === count($breadcrumb) - 1 ? 'breadcrumb-current' : ''; ?>">
                                                <?php echo htmlspecialchars($crumb['name']); ?>
                                            </span>
                                        </div>
                                    <?php endforeach; ?>
                                </nav>
                            </div>
                        <?php endif; ?>

                        <!-- File Statistics -->
                        <?php if (!empty($ftp_files)): ?>
                            <div class="file-stats">
                                <div class="file-stats-item">
                                    <span class="file-stats-value"><?php echo count($ftp_files); ?></span>
                                    <span class="file-stats-label">Total Files</span>
                                </div>
                                <div class="file-stats-item">
                                    <span class="file-stats-value">
                                        <?php
                                        $total_size = array_sum(array_column($ftp_files, 'size'));
                                        echo formatFileSize($total_size);
                                        ?>
                                    </span>
                                    <span class="file-stats-label">Total Size</span>
                                </div>
                                <div class="file-stats-item">
                                    <span class="file-stats-value">
                                        <?php
                                        $latest_file = max(array_column($ftp_files, 'modified'));
                                        echo date('M j, Y', $latest_file);
                                        ?>
                                    </span>
                                    <span class="file-stats-label">Latest File</span>
                                </div>
                            </div>

                            <!-- File List -->
                            <div class="files-table-container">
                                <table class="files-table">
                                    <thead>
                                        <tr>
                                            <th>📄 File Name</th>
                                            <th>📊 Size</th>
                                            <th>🕒 Modified</th>
                                            <th>⚡ Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($ftp_files as $file): ?>
                                            <tr>
                                                <td>
                                                    <div class="file-item">
                                                        <span class="file-icon">
                                                            <?php
                                                            $ext = strtolower(pathinfo($file['filename'], PATHINFO_EXTENSION));
                                                            switch ($ext) {
                                                                case 'pdf': echo '📄'; break;
                                                                case 'doc':
                                                                case 'docx': echo '📝'; break;
                                                                case 'xls':
                                                                case 'xlsx': echo '📊'; break;
                                                                case 'zip':
                                                                case 'rar': echo '🗜️'; break;
                                                                case 'jpg':
                                                                case 'jpeg':
                                                                case 'png':
                                                                case 'gif': echo '🖼️'; break;
                                                                default: echo '📄'; break;
                                                            }
                                                            ?>
                                                        </span>
                                                        <span class="file-name"><?php echo htmlspecialchars($file['filename']); ?></span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="file-size"><?php echo formatFileSize($file['size']); ?></span>
                                                </td>
                                                <td>
                                                    <span class="file-date"><?php echo date('M j, Y H:i', $file['modified']); ?></span>
                                                </td>
                                                <td>
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                                        <input type="hidden" name="action" value="download_file">
                                                        <input type="hidden" name="user_id" value="<?php echo $current_user_id; ?>">
                                                        <input type="hidden" name="file_path" value="<?php echo htmlspecialchars($file['full_path']); ?>">
                                                        <button type="submit" class="btn-download">
                                                            <span>⬇️</span>
                                                            Download
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <div class="empty-state-icon">📁</div>
                                <h3>No Files Found</h3>
                                <p>This user's FTP folder is empty or could not be accessed.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading FTP files...</p>
        </div>
    </div>

    <script>
        // Show loading overlay when browsing FTP
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            const loadingOverlay = document.getElementById('loadingOverlay');

            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const action = form.querySelector('input[name="action"]')?.value;

                    if (action === 'browse_user_ftp') {
                        const userSelect = form.querySelector('select[name="user_id"]');
                        if (userSelect && userSelect.value) {
                            loadingOverlay.style.display = 'flex';
                        }
                    }

                    if (action === 'download_file') {
                        // Show brief loading for downloads
                        const button = form.querySelector('button[type="submit"]');
                        const originalText = button.innerHTML;
                        button.innerHTML = '<span>⏳</span> Downloading...';
                        button.disabled = true;

                        setTimeout(() => {
                            button.innerHTML = originalText;
                            button.disabled = false;
                        }, 2000);
                    }
                });
            });

            // Enhanced user selection
            const userSelect = document.querySelector('select[name="user_id"]');
            if (userSelect) {
                userSelect.addEventListener('change', function() {
                    const browseButton = document.querySelector('.btn-browse');
                    if (this.value) {
                        browseButton.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
                        browseButton.innerHTML = '<span>🚀</span> Browse FTP';
                    } else {
                        browseButton.style.background = 'linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 100%)';
                        browseButton.innerHTML = '<span>🔍</span> Browse FTP';
                    }
                });
            }

            // File table enhancements
            const fileRows = document.querySelectorAll('.files-table tbody tr');
            fileRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(4px)';
                    this.style.transition = 'transform 0.2s ease';
                });

                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                });
            });

            // Add tooltips to file icons
            const fileIcons = document.querySelectorAll('.file-icon');
            fileIcons.forEach(icon => {
                const fileName = icon.parentElement.querySelector('.file-name').textContent;
                const ext = fileName.split('.').pop().toLowerCase();
                let tooltip = '';

                switch (ext) {
                    case 'pdf': tooltip = 'PDF Document'; break;
                    case 'doc':
                    case 'docx': tooltip = 'Word Document'; break;
                    case 'xls':
                    case 'xlsx': tooltip = 'Excel Spreadsheet'; break;
                    case 'zip':
                    case 'rar': tooltip = 'Archive File'; break;
                    case 'jpg':
                    case 'jpeg':
                    case 'png':
                    case 'gif': tooltip = 'Image File'; break;
                    default: tooltip = 'Document'; break;
                }

                icon.title = tooltip;
            });
        });
    </script>

<?php
/**
 * Format file size in human readable format
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>
</body>
</html>
