<?php
/**
 * Company User Management
 * Company Admin interface for managing users within their company
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../classes/User.php';
require_once '../shared/admin_config.php';

// Require login and company admin privileges
requireLogin();
requireAdminAccess('company');

$database = new Database();
$db = $database->getConnection();
$user = new User($db);

$error_message = '';
$success_message = '';

// Get current user's company ID
$company_id = $_SESSION['company_id'];

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCsrfToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        $user_id = $_POST['user_id'] ?? '';
        
        // Verify user belongs to current company
        $verify_query = "SELECT company_id FROM users WHERE id = ?";
        $verify_stmt = $db->prepare($verify_query);
        $verify_stmt->execute([$user_id]);
        $user_company = $verify_stmt->fetchColumn();
        
        if ($user_company != $company_id) {
            $error_message = 'You can only manage users from your own company.';
        } else {
            switch ($action) {
                case 'activate':
                    $query = "UPDATE users SET status = 'active' WHERE id = ? AND company_id = ?";
                    $stmt = $db->prepare($query);
                    if ($stmt->execute([$user_id, $company_id])) {
                        $success_message = 'User activated successfully.';
                        logActivity('user_updated', "User ID {$user_id} activated", $_SESSION['user_id'], $company_id);
                    } else {
                        $error_message = 'Failed to activate user.';
                    }
                    break;
                    
                case 'suspend':
                    // Don't allow suspending self
                    if ($user_id == $_SESSION['user_id']) {
                        $error_message = 'You cannot suspend your own account.';
                    } else {
                        $query = "UPDATE users SET status = 'suspended' WHERE id = ? AND company_id = ?";
                        $stmt = $db->prepare($query);
                        if ($stmt->execute([$user_id, $company_id])) {
                            $success_message = 'User suspended successfully.';
                            logActivity('user_updated', "User ID {$user_id} suspended", $_SESSION['user_id'], $company_id);
                        } else {
                            $error_message = 'Failed to suspend user.';
                        }
                    }
                    break;
                    
                case 'update_role':
                    $new_role = $_POST['new_role'] ?? '';
                    
                    // Don't allow changing own role or promoting to master_admin
                    if ($user_id == $_SESSION['user_id']) {
                        $error_message = 'You cannot change your own role.';
                    } elseif ($new_role === 'master_admin') {
                        $error_message = 'You cannot promote users to Master Admin.';
                    } elseif (!in_array($new_role, ['user', 'company_admin'])) {
                        $error_message = 'Invalid role selected.';
                    } else {
                        $query = "UPDATE users SET role = ? WHERE id = ? AND company_id = ?";
                        $stmt = $db->prepare($query);
                        if ($stmt->execute([$new_role, $user_id, $company_id])) {
                            $success_message = 'User role updated successfully.';
                            logActivity('user_updated', "User ID {$user_id} role changed to {$new_role}", $_SESSION['user_id'], $company_id);
                        } else {
                            $error_message = 'Failed to update user role.';
                        }
                    }
                    break;
            }
        }
    }
}

// Get company users
$search = $_GET['search'] ?? '';
$role_filter = $_GET['role'] ?? '';
$status_filter = $_GET['status'] ?? '';

$query = "SELECT u.*, 
                 COUNT(DISTINCT al.id) as activity_count,
                 COUNT(DISTINCT p.id) as payment_count
          FROM users u 
          LEFT JOIN activity_logs al ON u.id = al.user_id AND DATE(al.created_at) >= DATE_SUB(NOW(), INTERVAL 30 DAY)
          LEFT JOIN payments p ON u.id = p.user_id AND DATE(p.created_at) >= DATE_SUB(NOW(), INTERVAL 30 DAY)
          WHERE u.company_id = ? AND u.status != 'deleted'";

$params = [$company_id];

if ($search) {
    $query .= " AND (u.name LIKE ? OR u.email LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($role_filter) {
    $query .= " AND u.role = ?";
    $params[] = $role_filter;
}

if ($status_filter) {
    $query .= " AND u.status = ?";
    $params[] = $status_filter;
}

$query .= " GROUP BY u.id ORDER BY u.created_at DESC";

$stmt = $db->prepare($query);
$stmt->execute($params);
$users = $stmt->fetchAll();

// Get company info
$company_query = "SELECT company_name FROM companies WHERE id = ?";
$company_stmt = $db->prepare($company_query);
$company_stmt->execute([$company_id]);
$company_info = $company_stmt->fetch();

$csrf_token = generateCsrfToken();
$page_title = "Company Users";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('company'); ?>
    <style>
        .search-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-input {
            flex: 1;
            min-width: 250px;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
        }
        
        .filter-select {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
            min-width: 150px;
        }
        
        .users-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
        }
        
        .users-table th {
            background: #2B5E5F;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }
        
        .users-table td {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .users-table tr:hover {
            background: #f8fafa;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-active {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-suspended {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .role-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .role-company_admin {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .role-user {
            background: #f3f4f6;
            color: #374151;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 2rem;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .modal-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #2B5E5F;
        }
        
        .close {
            font-size: 2rem;
            cursor: pointer;
            color: #999;
        }
        
        .close:hover {
            color: #333;
        }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'company', 'users.php'); ?>
    
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Company Users</h1>
            <p class="page-subtitle">Manage users in <?php echo htmlspecialchars($company_info['company_name']); ?></p>
        </div>
        
        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <div class="admin-card">
            <!-- Search and Filters -->
            <form method="GET" class="search-filters">
                <input type="text" name="search" class="search-input" 
                       placeholder="Search users by name or email..." 
                       value="<?php echo htmlspecialchars($search); ?>">
                
                <select name="role" class="filter-select">
                    <option value="">All Roles</option>
                    <option value="company_admin" <?php echo $role_filter === 'company_admin' ? 'selected' : ''; ?>>Company Admin</option>
                    <option value="user" <?php echo $role_filter === 'user' ? 'selected' : ''; ?>>User</option>
                </select>
                
                <select name="status" class="filter-select">
                    <option value="">All Statuses</option>
                    <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                    <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                </select>
                
                <button type="submit" class="btn">Search</button>
                <a href="users.php" class="btn btn-secondary">Clear</a>
                <a href="user_create.php" class="btn btn-success">Add User</a>
            </form>
            
            <!-- Users Table -->
            <table class="users-table">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Role</th>
                        <th>Customs Code</th>
                        <th>Status</th>
                        <th>Activity (30d)</th>
                        <th>Last Login</th>
                        <th>Joined</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user_item): ?>
                        <tr>
                            <td>
                                <div style="font-weight: 600;"><?php echo htmlspecialchars($user_item['name']); ?></div>
                                <div style="font-size: 0.85rem; color: #666;"><?php echo htmlspecialchars($user_item['email']); ?></div>
                                <?php if ($user_item['id'] == $_SESSION['user_id']): ?>
                                    <div style="font-size: 0.75rem; color: #2B5E5F; font-weight: 600;">(You)</div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="role-badge role-<?php echo $user_item['role']; ?>">
                                    <?php echo ucfirst(str_replace('_', ' ', $user_item['role'])); ?>
                                </span>
                            </td>
                            <td>
                                <span style="background: #2B5E5F; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-family: 'Courier New', monospace; font-weight: 600; font-size: 0.85rem;">
                                    <?php echo htmlspecialchars($_SESSION['customs_code'] ?? 'Not Assigned'); ?>
                                </span>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $user_item['status']; ?>">
                                    <?php echo ucfirst($user_item['status']); ?>
                                </span>
                            </td>
                            <td>
                                <div><?php echo number_format($user_item['activity_count']); ?> activities</div>
                                <div style="font-size: 0.85rem; color: #666;"><?php echo number_format($user_item['payment_count']); ?> payments</div>
                            </td>
                            <td>
                                <?php if ($user_item['last_login']): ?>
                                    <?php echo date('M j, Y H:i', strtotime($user_item['last_login'])); ?>
                                <?php else: ?>
                                    <span style="color: #999;">Never</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php echo date('M j, Y', strtotime($user_item['created_at'])); ?>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="user_edit.php?id=<?php echo $user_item['id']; ?>" class="btn btn-sm">Edit</a>
                                    <a href="../master/ftp_browser.php?user_id=<?php echo $user_item['id']; ?>" class="btn btn-sm btn-secondary" title="Browse User's FTP Folder">FTP</a>

                                    <?php if ($user_item['id'] != $_SESSION['user_id']): // Don't allow actions on self ?>
                                        <button onclick="showRoleModal(<?php echo $user_item['id']; ?>, '<?php echo $user_item['role']; ?>')" class="btn btn-sm btn-secondary">Role</button>
                                        
                                        <?php if ($user_item['status'] === 'active'): ?>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to suspend this user?')">
                                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                                <input type="hidden" name="action" value="suspend">
                                                <input type="hidden" name="user_id" value="<?php echo $user_item['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-warning">Suspend</button>
                                            </form>
                                        <?php else: ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                                <input type="hidden" name="action" value="activate">
                                                <input type="hidden" name="user_id" value="<?php echo $user_item['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-success">Activate</button>
                                            </form>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <?php if (empty($users)): ?>
                <div style="text-align: center; padding: 3rem; color: #666;">
                    <p>No users found.</p>
                    <a href="user_create.php" class="btn" style="margin-top: 1rem;">Add First User</a>
                </div>
            <?php endif; ?>
        </div>
    </main>
    
    <!-- Role Change Modal -->
    <div id="roleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Change User Role</h3>
                <span class="close" onclick="closeRoleModal()">&times;</span>
            </div>
            <form method="POST" id="roleForm">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="action" value="update_role">
                <input type="hidden" name="user_id" id="roleUserId">
                
                <div class="form-group" style="margin-bottom: 1.5rem;">
                    <label style="font-weight: 600; margin-bottom: 0.5rem; display: block;">Select New Role:</label>
                    <select name="new_role" id="newRole" class="filter-select" style="width: 100%;" required>
                        <option value="user">User</option>
                        <option value="company_admin">Company Admin</option>
                    </select>
                    <div style="font-size: 0.85rem; color: #666; margin-top: 0.5rem;">
                        Company Admins can manage other users and company settings.
                    </div>
                </div>
                
                <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                    <button type="button" onclick="closeRoleModal()" class="btn btn-secondary">Cancel</button>
                    <button type="submit" class="btn">Update Role</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        function showRoleModal(userId, currentRole) {
            document.getElementById('roleUserId').value = userId;
            document.getElementById('newRole').value = currentRole;
            document.getElementById('roleModal').style.display = 'block';
        }
        
        function closeRoleModal() {
            document.getElementById('roleModal').style.display = 'none';
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('roleModal');
            if (event.target == modal) {
                closeRoleModal();
            }
        }
    </script>
</body>
</html>
