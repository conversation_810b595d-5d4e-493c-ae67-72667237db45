-- ============================================================================
-- FOREIGN KEY CONSTRAINT FIX
-- ============================================================================
-- Run this script if you're getting foreign key constraint errors
-- This will safely drop and recreate just the problematic tables
-- ============================================================================

-- Step 1: Disable foreign key checks
SET FOREIGN_KEY_CHECKS = 0;

-- Step 2: Drop the users table (this was causing the error)
DROP TABLE IF EXISTS users;

-- Step 3: Recreate the users table without the ftp_identification_code field
CREATE TABLE users (
    id INT(11) NOT NULL AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('master_admin', 'company_admin', 'user') NOT NULL DEFAULT 'user',
    company_id INT(11) DEFAULT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL DEFAULT NULL,
    status ENUM('active', 'inactive', 'suspended') NOT NULL DEFAULT 'active',
    created_by INT(11) DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY unique_email_company (email, company_id),
    KEY idx_email (email),
    KEY idx_company_id (company_id),
    KEY idx_role (role),
    KEY idx_status (status),
    KEY fk_users_created_by (created_by),
    CONSTRAINT fk_users_company FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    CONSTRAINT fk_users_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Step 4: Re-insert the sample users
-- Master Admin user (password: master123)
INSERT INTO users (name, email, password, role, company_id) VALUES
('Master Administrator', '<EMAIL>', '$2y$12$LTVXDglo.oxDbeCgcLoBXOOqvJT3XkvFkFew57YWkRrmH73hYBsZC', 'master_admin', 1);

-- Demo Company Admin (password: admin123)
INSERT INTO users (name, email, password, role, company_id, created_by) VALUES
('Demo Company Admin', '<EMAIL>', '$2y$12$39Nxqv7VLiBH.er7PylfNuu783noTlT9VH/WFWIL7/wRsU3wBQ7i2', 'company_admin', 2, 1);

-- Sample Company Admin (password: admin123)
INSERT INTO users (name, email, password, role, company_id, created_by) VALUES
('Sample Company Admin', '<EMAIL>', '$2y$12$39Nxqv7VLiBH.er7PylfNuu783noTlT9VH/WFWIL7/wRsU3wBQ7i2', 'company_admin', 3, 1);

-- Demo regular users (password: admin123 for all)
INSERT INTO users (name, email, password, role, company_id, created_by) VALUES
('Demo User 1', '<EMAIL>', '$2y$12$39Nxqv7VLiBH.er7PylfNuu783noTlT9VH/WFWIL7/wRsU3wBQ7i2', 'user', 2, 2),
('Demo User 2', '<EMAIL>', '$2y$12$39Nxqv7VLiBH.er7PylfNuu783noTlT9VH/WFWIL7/wRsU3wBQ7i2', 'user', 2, 2),
('Sample User 1', '<EMAIL>', '$2y$12$39Nxqv7VLiBH.er7PylfNuu783noTlT9VH/WFWIL7/wRsU3wBQ7i2', 'user', 3, 3);

-- Step 5: Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================
-- Run these to verify everything is working:

-- Check users table structure
-- DESCRIBE users;

-- Check that users were created successfully
-- SELECT u.id, u.name, u.email, u.role, c.company_name, c.customs_code 
-- FROM users u 
-- LEFT JOIN companies c ON u.company_id = c.id 
-- ORDER BY u.id;

-- Check foreign key constraints
-- SELECT 
--     TABLE_NAME,
--     COLUMN_NAME,
--     CONSTRAINT_NAME,
--     REFERENCED_TABLE_NAME,
--     REFERENCED_COLUMN_NAME
-- FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
-- WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
--   AND REFERENCED_TABLE_NAME = 'users';

-- ============================================================================
-- SUCCESS!
-- ============================================================================
-- The users table has been recreated without the ftp_identification_code field.
-- The application now uses the customs_code from the companies table instead.
-- All foreign key constraints should now work properly.
-- ============================================================================
