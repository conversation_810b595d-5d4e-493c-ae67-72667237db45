<?php
/**
 * FTP System Validation Tool
 * Validates the FTP connectivity implementation and user identification codes
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireAdminAccess('master');

$database = new Database();
$db = $database->getConnection();

$validation_results = [];
$overall_status = 'success';

// Generate admin header
echo generateAdminHeader('master', 'FTP System Validation');
?>

<div class="admin-container">
    <div class="admin-header">
        <h1>🔍 FTP System Validation</h1>
        <p>Comprehensive validation of FTP connectivity implementation using company customs codes</p>
    </div>

    <?php
    // Validation Test 1: Database Schema Check
    $validation_results['schema'] = validateDatabaseSchema($db);
    
    // Validation Test 2: Company Customs Codes Check
    $validation_results['customs_codes'] = validateCompanyCustomsCodes($db);

    // Validation Test 3: Customs Code Uniqueness
    $validation_results['uniqueness'] = validateCustomsCodeUniqueness($db);
    
    // Validation Test 4: FTP Assignments Check
    $validation_results['assignments'] = validateFTPAssignments($db);
    
    // Validation Test 5: Security Functions Check
    $validation_results['security'] = validateSecurityFunctions();
    
    // Validation Test 6: File Structure Check
    $validation_results['files'] = validateFileStructure();
    
    // Determine overall status
    foreach ($validation_results as $test => $result) {
        if ($result['status'] !== 'success') {
            $overall_status = $result['status'];
            if ($result['status'] === 'error') {
                break; // Error is worse than warning
            }
        }
    }
    ?>

    <!-- Overall Status -->
    <div class="admin-card">
        <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
            <div style="font-size: 2rem;">
                <?php echo $overall_status === 'success' ? '✅' : ($overall_status === 'warning' ? '⚠️' : '❌'); ?>
            </div>
            <div>
                <h2 style="margin: 0; color: <?php echo $overall_status === 'success' ? '#10b981' : ($overall_status === 'warning' ? '#f59e0b' : '#dc2626'); ?>;">
                    System Status: <?php echo ucfirst($overall_status); ?>
                </h2>
                <p style="margin: 0; color: #666;">
                    <?php 
                    if ($overall_status === 'success') {
                        echo 'All validation tests passed. System is ready for deployment.';
                    } elseif ($overall_status === 'warning') {
                        echo 'Some issues detected that should be addressed before deployment.';
                    } else {
                        echo 'Critical issues detected. System requires fixes before deployment.';
                    }
                    ?>
                </p>
            </div>
        </div>
    </div>

    <!-- Detailed Results -->
    <?php foreach ($validation_results as $test_name => $result): ?>
        <div class="admin-card">
            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                <div style="font-size: 1.5rem;">
                    <?php echo $result['status'] === 'success' ? '✅' : ($result['status'] === 'warning' ? '⚠️' : '❌'); ?>
                </div>
                <h3 style="margin: 0; color: #2B5E5F;"><?php echo htmlspecialchars($result['title']); ?></h3>
            </div>
            
            <p style="color: #666; margin-bottom: 1rem;"><?php echo htmlspecialchars($result['description']); ?></p>
            
            <?php if (!empty($result['details'])): ?>
                <div style="background: #f8fafc; padding: 1rem; border-radius: 6px; margin-bottom: 1rem;">
                    <?php foreach ($result['details'] as $detail): ?>
                        <div style="margin-bottom: 0.5rem;">
                            <span style="color: <?php echo $detail['type'] === 'success' ? '#10b981' : ($detail['type'] === 'warning' ? '#f59e0b' : '#dc2626'); ?>;">
                                <?php echo $detail['type'] === 'success' ? '✓' : ($detail['type'] === 'warning' ? '⚠' : '✗'); ?>
                            </span>
                            <?php echo htmlspecialchars($detail['message']); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($result['recommendations'])): ?>
                <div style="background: #fef3c7; padding: 1rem; border-radius: 6px; border-left: 4px solid #f59e0b;">
                    <h4 style="margin: 0 0 0.5rem 0; color: #92400e;">Recommendations:</h4>
                    <ul style="margin: 0; padding-left: 1.5rem;">
                        <?php foreach ($result['recommendations'] as $recommendation): ?>
                            <li style="color: #92400e;"><?php echo htmlspecialchars($recommendation); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
    <?php endforeach; ?>

    <!-- Quick Actions -->
    <div class="admin-card">
        <h3 style="color: #2B5E5F; margin-bottom: 1rem;">Quick Actions</h3>
        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
            <a href="ftp_browser.php" class="btn-primary">Test FTP Browser</a>
            <a href="ftp_management.php" class="btn-secondary">FTP Management</a>
            <a href="users.php" class="btn-secondary">View Users</a>
            <button onclick="window.location.reload()" class="btn-secondary">Re-run Validation</button>
        </div>
    </div>
</div>

<?php
/**
 * Validate database schema for FTP identification codes
 */
function validateDatabaseSchema($db) {
    $result = [
        'title' => 'Database Schema Validation',
        'description' => 'Checking if the database schema includes customs code fields',
        'status' => 'success',
        'details' => [],
        'recommendations' => []
    ];

    try {
        // Check if customs_code column exists in companies table
        $query = "SHOW COLUMNS FROM companies LIKE 'customs_code'";
        $stmt = $db->prepare($query);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $result['details'][] = ['type' => 'success', 'message' => 'Customs code column exists in companies table'];
        } else {
            $result['status'] = 'error';
            $result['details'][] = ['type' => 'error', 'message' => 'Customs code column missing from companies table'];
            $result['recommendations'][] = 'Run the database migration: database/migrations/add_customs_codes_for_ftp.sql';
        }

        // Check for index on customs codes
        $query = "SHOW INDEX FROM companies WHERE Key_name = 'idx_customs_code'";
        $stmt = $db->prepare($query);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $result['details'][] = ['type' => 'success', 'message' => 'Index exists for customs codes'];
        } else {
            $result['status'] = 'warning';
            $result['details'][] = ['type' => 'warning', 'message' => 'Index missing for customs codes'];
            $result['recommendations'][] = 'Add index for customs_code column for better performance';
        }

    } catch (Exception $e) {
        $result['status'] = 'error';
        $result['details'][] = ['type' => 'error', 'message' => 'Database error: ' . $e->getMessage()];
    }

    return $result;
}

/**
 * Validate company customs codes
 */
function validateCompanyCustomsCodes($db) {
    $result = [
        'title' => 'Company Customs Codes Validation',
        'description' => 'Checking if all companies have valid customs codes',
        'status' => 'success',
        'details' => [],
        'recommendations' => []
    ];

    try {
        // Count total active companies
        $query = "SELECT COUNT(*) as total FROM companies WHERE status = 'active'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $total_companies = $stmt->fetchColumn();

        // Count companies with customs codes
        $query = "SELECT COUNT(*) as with_codes FROM companies WHERE status = 'active' AND customs_code IS NOT NULL AND customs_code != ''";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $companies_with_codes = $stmt->fetchColumn();

        $result['details'][] = ['type' => 'success', 'message' => "Total active companies: {$total_companies}"];
        $result['details'][] = ['type' => 'success', 'message' => "Companies with customs codes: {$companies_with_codes}"];

        if ($companies_with_codes < $total_companies) {
            $missing = $total_companies - $companies_with_codes;
            $result['status'] = 'warning';
            $result['details'][] = ['type' => 'warning', 'message' => "Companies missing customs codes: {$missing}"];
            $result['recommendations'][] = 'Run the customs code generation migration for existing companies';
        }

        // Check code format
        $query = "SELECT customs_code FROM companies WHERE status = 'active' AND customs_code IS NOT NULL";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $codes = $stmt->fetchAll(PDO::FETCH_COLUMN);

        $valid_format = 0;
        foreach ($codes as $code) {
            if (preg_match('/^[A-Z0-9]{6,12}$/', $code)) {
                $valid_format++;
            }
        }

        if ($valid_format === count($codes)) {
            $result['details'][] = ['type' => 'success', 'message' => 'All customs codes have valid format'];
        } else {
            $invalid = count($codes) - $valid_format;
            $result['status'] = 'warning';
            $result['details'][] = ['type' => 'warning', 'message' => "Invalid customs code formats: {$invalid}"];
            $result['recommendations'][] = 'Review and regenerate invalid customs codes';
        }

    } catch (Exception $e) {
        $result['status'] = 'error';
        $result['details'][] = ['type' => 'error', 'message' => 'Error validating customs codes: ' . $e->getMessage()];
    }

    return $result;
}

/**
 * Validate customs code uniqueness
 */
function validateCustomsCodeUniqueness($db) {
    $result = [
        'title' => 'Customs Code Uniqueness Validation',
        'description' => 'Checking for duplicate customs codes',
        'status' => 'success',
        'details' => [],
        'recommendations' => []
    ];

    try {
        $query = "SELECT customs_code, COUNT(*) as count
                  FROM companies
                  WHERE customs_code IS NOT NULL AND customs_code != ''
                  GROUP BY customs_code
                  HAVING COUNT(*) > 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $duplicates = $stmt->fetchAll();

        if (empty($duplicates)) {
            $result['details'][] = ['type' => 'success', 'message' => 'All customs codes are unique'];
        } else {
            $result['status'] = 'error';
            foreach ($duplicates as $dup) {
                $result['details'][] = ['type' => 'error', 'message' => "Duplicate customs code '{$dup['customs_code']}' found {$dup['count']} times"];
            }
            $result['recommendations'][] = 'Regenerate duplicate customs codes';
        }

    } catch (Exception $e) {
        $result['status'] = 'error';
        $result['details'][] = ['type' => 'error', 'message' => 'Error checking uniqueness: ' . $e->getMessage()];
    }

    return $result;
}

/**
 * Validate FTP assignments
 */
function validateFTPAssignments($db) {
    $result = [
        'title' => 'FTP Assignments Validation',
        'description' => 'Checking FTP server assignments and configurations',
        'status' => 'success',
        'details' => [],
        'recommendations' => []
    ];
    
    try {
        // Count active FTP servers
        $query = "SELECT COUNT(*) FROM ftp_servers WHERE is_active = TRUE";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $active_servers = $stmt->fetchColumn();
        
        $result['details'][] = ['type' => 'success', 'message' => "Active FTP servers: {$active_servers}"];
        
        if ($active_servers === 0) {
            $result['status'] = 'warning';
            $result['details'][] = ['type' => 'warning', 'message' => 'No active FTP servers configured'];
            $result['recommendations'][] = 'Configure at least one FTP server for file operations';
        }
        
        // Count active assignments
        $query = "SELECT COUNT(*) FROM company_ftp_assignments WHERE is_active = TRUE";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $active_assignments = $stmt->fetchColumn();
        
        $result['details'][] = ['type' => 'success', 'message' => "Active FTP assignments: {$active_assignments}"];
        
        if ($active_assignments === 0) {
            $result['status'] = 'warning';
            $result['details'][] = ['type' => 'warning', 'message' => 'No active FTP assignments found'];
            $result['recommendations'][] = 'Assign FTP servers to companies for user access';
        }
        
    } catch (Exception $e) {
        $result['status'] = 'error';
        $result['details'][] = ['type' => 'error', 'message' => 'Error validating FTP assignments: ' . $e->getMessage()];
    }
    
    return $result;
}

/**
 * Validate security functions
 */
function validateSecurityFunctions() {
    $result = [
        'title' => 'Security Functions Validation',
        'description' => 'Checking if security functions are properly implemented',
        'status' => 'success',
        'details' => [],
        'recommendations' => []
    ];
    
    // Check if FTPManager class exists
    if (class_exists('FTPManager')) {
        $result['details'][] = ['type' => 'success', 'message' => 'FTPManager class is available'];
    } else {
        $result['status'] = 'error';
        $result['details'][] = ['type' => 'error', 'message' => 'FTPManager class not found'];
        $result['recommendations'][] = 'Ensure FTPManager class is properly included';
    }
    
    // Check if validation functions exist
    if (function_exists('validateUserFTPAccess')) {
        $result['details'][] = ['type' => 'success', 'message' => 'FTP access validation function exists'];
    } else {
        $result['status'] = 'warning';
        $result['details'][] = ['type' => 'warning', 'message' => 'FTP access validation function not found'];
        $result['recommendations'][] = 'Implement validateUserFTPAccess function for security';
    }
    
    // Check if getUserFTPPath function exists
    if (function_exists('getUserFTPPath')) {
        $result['details'][] = ['type' => 'success', 'message' => 'User FTP path function exists'];
    } else {
        $result['status'] = 'error';
        $result['details'][] = ['type' => 'error', 'message' => 'getUserFTPPath function not found'];
        $result['recommendations'][] = 'Implement getUserFTPPath function';
    }
    
    return $result;
}

/**
 * Validate file structure
 */
function validateFileStructure() {
    $result = [
        'title' => 'File Structure Validation',
        'description' => 'Checking if required files and directories exist',
        'status' => 'success',
        'details' => [],
        'recommendations' => []
    ];
    
    $required_files = [
        '../../classes/FTPManager.php' => 'FTPManager class file',
        'ftp_browser.php' => 'FTP browser interface',
        'ftp_management.php' => 'FTP management interface',
        '../../database/migrations/add_ftp_identification_codes.sql' => 'FTP migration script'
    ];
    
    foreach ($required_files as $file => $description) {
        if (file_exists($file)) {
            $result['details'][] = ['type' => 'success', 'message' => "{$description} exists"];
        } else {
            $result['status'] = 'warning';
            $result['details'][] = ['type' => 'warning', 'message' => "{$description} not found"];
            $result['recommendations'][] = "Ensure {$description} is properly deployed";
        }
    }
    
    return $result;
}
?>
</body>
</html>
