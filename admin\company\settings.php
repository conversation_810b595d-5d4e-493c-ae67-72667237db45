<?php
/**
 * Company Settings
 * Company Admin interface for managing company-specific settings
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../shared/admin_config.php';

// Require login and company admin privileges
requireLogin();
requireAdminAccess('company');

$database = new Database();
$db = $database->getConnection();

// Get current user's company ID
$company_id = $_SESSION['company_id'];

$error_message = '';
$success_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCsrfToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_company_info':
                $company_name = trim($_POST['company_name'] ?? '');
                $contact_email = trim($_POST['contact_email'] ?? '');
                $contact_phone = trim($_POST['contact_phone'] ?? '');
                $address = trim($_POST['address'] ?? '');
                
                if (empty($company_name)) {
                    $error_message = 'Company name is required.';
                } elseif (!filter_var($contact_email, FILTER_VALIDATE_EMAIL)) {
                    $error_message = 'Please enter a valid email address.';
                } else {
                    $query = "UPDATE companies SET
                              company_name = ?,
                              email = ?,
                              telephone = ?,
                              address = ?,
                              updated_at = NOW()
                              WHERE id = ?";
                    
                    $stmt = $db->prepare($query);
                    if ($stmt->execute([$company_name, $contact_email, $contact_phone, $address, $company_id])) {
                        $success_message = 'Company information updated successfully.';
                        logActivity('company_updated', 'Company information updated', $_SESSION['user_id'], $company_id);
                    } else {
                        $error_message = 'Failed to update company information.';
                    }
                }
                break;
                
            case 'update_ftp_settings':
                $ftp_host = trim($_POST['ftp_host'] ?? '');
                $ftp_username = trim($_POST['ftp_username'] ?? '');
                $ftp_password = $_POST['ftp_password'] ?? '';
                $ftp_port = intval($_POST['ftp_port'] ?? 21);
                
                if (empty($ftp_host) || empty($ftp_username)) {
                    $error_message = 'FTP host and username are required.';
                } else {
                    // Test FTP connection
                    $ftp_conn = ftp_connect($ftp_host, $ftp_port);
                    if ($ftp_conn && ftp_login($ftp_conn, $ftp_username, $ftp_password)) {
                        ftp_close($ftp_conn);
                        
                        // Update FTP settings
                        $query = "UPDATE companies SET 
                                  ftp_host = ?, 
                                  ftp_username = ?, 
                                  ftp_password = ?, 
                                  ftp_port = ?,
                                  updated_at = NOW()
                                  WHERE id = ?";
                        
                        $stmt = $db->prepare($query);
                        if ($stmt->execute([$ftp_host, $ftp_username, $ftp_password, $ftp_port, $company_id])) {
                            $success_message = 'FTP settings updated successfully.';
                            logActivity('ftp_settings_updated', 'FTP settings updated', $_SESSION['user_id'], $company_id);
                        } else {
                            $error_message = 'Failed to update FTP settings.';
                        }
                    } else {
                        $error_message = 'Failed to connect to FTP server. Please check your credentials.';
                        if ($ftp_conn) ftp_close($ftp_conn);
                    }
                }
                break;
                
            case 'update_notification_settings':
                $email_notifications = isset($_POST['email_notifications']) ? 1 : 0;
                $payment_notifications = isset($_POST['payment_notifications']) ? 1 : 0;
                $user_notifications = isset($_POST['user_notifications']) ? 1 : 0;
                $system_notifications = isset($_POST['system_notifications']) ? 1 : 0;
                
                $query = "UPDATE companies SET 
                          email_notifications = ?, 
                          payment_notifications = ?, 
                          user_notifications = ?, 
                          system_notifications = ?,
                          updated_at = NOW()
                          WHERE id = ?";
                
                $stmt = $db->prepare($query);
                if ($stmt->execute([$email_notifications, $payment_notifications, $user_notifications, $system_notifications, $company_id])) {
                    $success_message = 'Notification settings updated successfully.';
                    logActivity('notification_settings_updated', 'Notification settings updated', $_SESSION['user_id'], $company_id);
                } else {
                    $error_message = 'Failed to update notification settings.';
                }
                break;
        }
    }
}

// Get current company settings
$query = "SELECT * FROM companies WHERE id = ?";
$stmt = $db->prepare($query);
$stmt->execute([$company_id]);
$company = $stmt->fetch();

if (!$company) {
    header('Location: ../../dashboard.php');
    exit();
}

$csrf_token = generateCsrfToken();
$page_title = "Company Settings";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('company'); ?>
    <style>
        .settings-tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 2rem;
            gap: 0;
        }
        
        .tab-button {
            padding: 1rem 2rem;
            background: none;
            border: none;
            border-bottom: 3px solid transparent;
            cursor: pointer;
            font-weight: 600;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .tab-button.active {
            color: #2B5E5F;
            border-bottom-color: #2B5E5F;
        }
        
        .tab-button:hover {
            color: #2B5E5F;
            background: #f8fafa;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: #2B5E5F;
            margin-bottom: 0.5rem;
        }
        
        .form-input, .form-textarea, .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus, .form-textarea:focus, .form-select:focus {
            outline: none;
            border-color: #2B5E5F;
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .checkbox-input {
            width: auto;
        }
        
        .checkbox-label {
            margin: 0;
            font-weight: normal;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        
        .settings-section {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
            margin-bottom: 2rem;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #2B5E5F;
            margin-bottom: 1rem;
        }
        
        .section-description {
            color: #666;
            margin-bottom: 1.5rem;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e5e7eb;
        }
        
        .info-box {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .info-box-title {
            font-weight: 600;
            color: #0c4a6e;
            margin-bottom: 0.5rem;
        }
        
        .info-box-text {
            color: #0c4a6e;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'company', 'settings.php'); ?>
    
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Company Settings</h1>
            <p class="page-subtitle">Manage settings for <?php echo htmlspecialchars($company['company_name']); ?></p>
        </div>
        
        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Settings Tabs -->
        <div class="settings-tabs">
            <button class="tab-button active" onclick="showTab('company-info')">Company Information</button>
            <button class="tab-button" onclick="showTab('ftp-settings')">FTP Settings</button>
            <button class="tab-button" onclick="showTab('notifications')">Notifications</button>
            <button class="tab-button" onclick="showTab('security')">Security</button>
        </div>
        
        <!-- Company Information Tab -->
        <div id="company-info" class="tab-content active">
            <div class="settings-section">
                <h3 class="section-title">Company Information</h3>
                <p class="section-description">Update your company's basic information and contact details.</p>
                
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="update_company_info">
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">Company Name *</label>
                            <input type="text" name="company_name" class="form-input" 
                                   value="<?php echo htmlspecialchars($company['company_name']); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Company Code</label>
                            <input type="text" class="form-input" 
                                   value="<?php echo htmlspecialchars($company['company_code']); ?>" readonly>
                            <small style="color: #666;">Company code cannot be changed</small>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Contact Email *</label>
                            <input type="email" name="contact_email" class="form-input"
                                   value="<?php echo htmlspecialchars($company['email'] ?? ''); ?>" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Contact Phone</label>
                            <input type="tel" name="contact_phone" class="form-input"
                                   value="<?php echo htmlspecialchars($company['telephone'] ?? ''); ?>">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Address</label>
                        <textarea name="address" class="form-textarea" 
                                  placeholder="Enter company address..."><?php echo htmlspecialchars($company['address'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn">Update Company Information</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- FTP Settings Tab -->
        <div id="ftp-settings" class="tab-content">
            <div class="settings-section">
                <h3 class="section-title">FTP Settings</h3>
                <p class="section-description">Configure FTP access for file management and invoice storage.</p>
                
                <div class="info-box">
                    <div class="info-box-title">FTP Information</div>
                    <div class="info-box-text">
                        Your FTP folder: <strong>/<?php echo htmlspecialchars($company['company_code']); ?>/</strong><br>
                        Use these credentials to access your dedicated FTP space for invoice files.
                    </div>
                </div>
                
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="update_ftp_settings">
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">FTP Host *</label>
                            <input type="text" name="ftp_host" class="form-input" 
                                   value="<?php echo htmlspecialchars($company['ftp_host'] ?? ''); ?>" 
                                   placeholder="ftp.example.com" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">FTP Port</label>
                            <input type="number" name="ftp_port" class="form-input" 
                                   value="<?php echo htmlspecialchars($company['ftp_port'] ?? 21); ?>" 
                                   min="1" max="65535">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">FTP Username *</label>
                            <input type="text" name="ftp_username" class="form-input" 
                                   value="<?php echo htmlspecialchars($company['ftp_username'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">FTP Password</label>
                            <input type="password" name="ftp_password" class="form-input" 
                                   placeholder="Enter new password or leave blank to keep current">
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn">Update FTP Settings</button>
                        <button type="button" onclick="testFtpConnection()" class="btn btn-secondary">Test Connection</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Notifications Tab -->
        <div id="notifications" class="tab-content">
            <div class="settings-section">
                <h3 class="section-title">Notification Settings</h3>
                <p class="section-description">Configure which notifications your company receives.</p>
                
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="update_notification_settings">
                    
                    <div class="checkbox-group">
                        <input type="checkbox" name="email_notifications" class="checkbox-input" 
                               <?php echo $company['email_notifications'] ? 'checked' : ''; ?>>
                        <label class="checkbox-label">Email Notifications</label>
                    </div>
                    <p style="color: #666; margin-bottom: 1.5rem; margin-left: 1.5rem;">
                        Receive general email notifications about system updates and announcements.
                    </p>
                    
                    <div class="checkbox-group">
                        <input type="checkbox" name="payment_notifications" class="checkbox-input" 
                               <?php echo $company['payment_notifications'] ? 'checked' : ''; ?>>
                        <label class="checkbox-label">Payment Notifications</label>
                    </div>
                    <p style="color: #666; margin-bottom: 1.5rem; margin-left: 1.5rem;">
                        Get notified about payment confirmations, failures, and pending transactions.
                    </p>
                    
                    <div class="checkbox-group">
                        <input type="checkbox" name="user_notifications" class="checkbox-input" 
                               <?php echo $company['user_notifications'] ? 'checked' : ''; ?>>
                        <label class="checkbox-label">User Activity Notifications</label>
                    </div>
                    <p style="color: #666; margin-bottom: 1.5rem; margin-left: 1.5rem;">
                        Receive notifications when users join, leave, or perform important actions.
                    </p>
                    
                    <div class="checkbox-group">
                        <input type="checkbox" name="system_notifications" class="checkbox-input" 
                               <?php echo $company['system_notifications'] ? 'checked' : ''; ?>>
                        <label class="checkbox-label">System Notifications</label>
                    </div>
                    <p style="color: #666; margin-bottom: 1.5rem; margin-left: 1.5rem;">
                        Get notified about system maintenance, security alerts, and important updates.
                    </p>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn">Update Notification Settings</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Security Tab -->
        <div id="security" class="tab-content">
            <div class="settings-section">
                <h3 class="section-title">Security Settings</h3>
                <p class="section-description">Manage security settings and access controls for your company.</p>
                
                <div class="info-box">
                    <div class="info-box-title">Company Status</div>
                    <div class="info-box-text">
                        Status: <strong><?php echo ucfirst($company['status']); ?></strong><br>
                        Created: <strong><?php echo date('M j, Y', strtotime($company['created_at'])); ?></strong><br>
                        Last Updated: <strong><?php echo $company['updated_at'] ? date('M j, Y H:i', strtotime($company['updated_at'])) : 'Never'; ?></strong>
                    </div>
                </div>
                
                <div style="margin-top: 2rem;">
                    <h4 style="color: #2B5E5F; margin-bottom: 1rem;">Security Actions</h4>
                    <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                        <a href="../company/users.php" class="btn btn-secondary">Manage Users</a>
                        <a href="../company/activity.php" class="btn btn-secondary">View Activity Logs</a>
                        <a href="../../profile.php" class="btn btn-secondary">Change Password</a>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked button
            event.target.classList.add('active');
        }
        
        function testFtpConnection() {
            const host = document.querySelector('input[name="ftp_host"]').value;
            const username = document.querySelector('input[name="ftp_username"]').value;
            const password = document.querySelector('input[name="ftp_password"]').value;
            const port = document.querySelector('input[name="ftp_port"]').value;
            
            if (!host || !username) {
                alert('Please enter FTP host and username first.');
                return;
            }
            
            // This would typically make an AJAX call to test the connection
            alert('FTP connection test would be performed here. This is a placeholder for the actual implementation.');
        }
    </script>
</body>
</html>
