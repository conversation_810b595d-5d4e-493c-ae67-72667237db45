-- FTP Credentials Management Schema
-- Additional tables for enhanced FTP and customs code management

-- FTP Servers table - Store multiple FTP server configurations
CREATE TABLE IF NOT EXISTS ftp_servers (
    id INT(11) NOT NULL AUTO_INCREMENT,
    server_name VARCHAR(255) NOT NULL,
    host VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    port INT(11) NOT NULL DEFAULT 21,
    username VA<PERSON>HA<PERSON>(255) NOT NULL,
    password_encrypted TEXT NOT NULL,
    base_path VARCHAR(500) DEFAULT '/',
    connection_type ENUM('ftp', 'sftp', 'ftps') NOT NULL DEFAULT 'ftp',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT(11) DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY unique_server_name (server_name),
    KEY idx_host (host),
    KEY idx_is_active (is_active),
    KEY idx_is_default (is_default),
    CONSTRAINT fk_ftp_servers_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Company FTP Assignments table - Link companies to FTP servers
CREATE TABLE IF NOT EXISTS company_ftp_assignments (
    id INT(11) NOT NULL AUTO_INCREMENT,
    company_id INT(11) NOT NULL,
    ftp_server_id INT(11) NOT NULL,
    custom_path VARCHAR(500) DEFAULT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    assigned_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT(11) DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY unique_company_ftp (company_id, ftp_server_id),
    KEY idx_company_id (company_id),
    KEY idx_ftp_server_id (ftp_server_id),
    KEY idx_is_active (is_active),
    CONSTRAINT fk_company_ftp_company FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    CONSTRAINT fk_company_ftp_server FOREIGN KEY (ftp_server_id) REFERENCES ftp_servers(id) ON DELETE CASCADE,
    CONSTRAINT fk_company_ftp_assigned_by FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System Settings table - Store system-wide configuration
CREATE TABLE IF NOT EXISTS system_settings (
    id INT(11) NOT NULL AUTO_INCREMENT,
    setting_key VARCHAR(255) NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'integer', 'boolean', 'json', 'encrypted') NOT NULL DEFAULT 'string',
    description TEXT,
    is_editable BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT(11) DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY unique_setting_key (setting_key),
    KEY idx_setting_key (setting_key),
    KEY idx_setting_type (setting_type),
    CONSTRAINT fk_system_settings_updated_by FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default FTP server configuration
INSERT INTO ftp_servers (server_name, host, port, username, password_encrypted, base_path, is_default, created_by) VALUES
('Default FTP Server', 'localhost', 21, 'ftpuser', AES_ENCRYPT('defaultpass', 'ftp_encryption_key_2024'), '/invoices/', TRUE, 1);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_editable) VALUES
('ftp_encryption_key', 'ftp_encryption_key_2024', 'encrypted', 'Encryption key for FTP passwords', FALSE),
('default_ftp_timeout', '30', 'integer', 'Default FTP connection timeout in seconds', TRUE),
('customs_code_format', '^[A-Z0-9]{6,12}$', 'string', 'Regex pattern for customs code validation', TRUE),
('max_ftp_connections', '10', 'integer', 'Maximum concurrent FTP connections', TRUE),
('ftp_passive_mode', 'true', 'boolean', 'Use passive mode for FTP connections', TRUE);

-- Update activity_logs enum to include new action types
ALTER TABLE activity_logs MODIFY COLUMN action ENUM(
    'login', 'logout', 'company_created', 'user_created', 'user_updated', 'user_deleted', 
    'payment_made', 'payment_failed', 'invoice_downloaded', 'permission_changed', 
    'system_error', 'ftp_server_created', 'ftp_server_updated', 'ftp_server_deleted',
    'ftp_assignment_created', 'ftp_assignment_updated', 'ftp_assignment_deleted',
    'customs_code_updated', 'system_settings_updated'
) NOT NULL;

-- Add indexes to companies table for better performance
ALTER TABLE companies ADD INDEX idx_customs_code (customs_code);
ALTER TABLE companies ADD INDEX idx_status (status);

-- Update companies table to add FTP-related fields if they don't exist
-- Note: Some of these may already exist, so we use IF NOT EXISTS where possible
ALTER TABLE companies 
ADD COLUMN IF NOT EXISTS ftp_host VARCHAR(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS ftp_username VARCHAR(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS ftp_password VARCHAR(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS ftp_port INT(11) DEFAULT 21;
