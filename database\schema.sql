-- ============================================================================
-- DATABASE RESET SCRIPT
-- ============================================================================
-- This script safely drops all tables and recreates the database schema
-- Use this when you need to completely reset the database
-- ============================================================================

-- Disable foreign key checks to allow dropping tables in any order
SET FOREIGN_KEY_CHECKS = 0;

-- Drop all tables if they exist
DROP TABLE IF EXISTS activity_logs;
DROP TABLE IF EXISTS user_sessions;
DROP TABLE IF EXISTS payments;
DROP TABLE IF EXISTS user_rights;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS companies;
DROP TABLE IF EXISTS company_ftp_assignments;
DROP TABLE IF EXISTS ftp_servers;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- RECREATE TABLES
-- ============================================================================

-- Companies table (tenants) - Enhanced for multi-tenant architecture
CREATE TABLE companies (
    id INT(11) NOT NULL AUTO_INCREMENT,
    company_name VARCHAR(255) NOT NULL,
    company_code VARCHAR(50) NOT NULL,
    customs_code VARCHAR(100) DEFAULT NULL,
    address TEXT DEFAULT NULL,
    telephone VARCHAR(50) DEFAULT NULL,
    email VARCHAR(255) DEFAULT NULL,
    company_reg_no VARCHAR(100) DEFAULT NULL,
    vat_no VARCHAR(100) DEFAULT NULL,
    ftp_folder_path VARCHAR(500) DEFAULT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'suspended') NOT NULL DEFAULT 'active',
    PRIMARY KEY (id),
    UNIQUE KEY unique_company_code (company_code),
    UNIQUE KEY unique_customs_code (customs_code),
    KEY idx_company_code (company_code),
    KEY idx_customs_code (customs_code),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Users table - Enhanced for multi-tenant architecture
CREATE TABLE users (
    id INT(11) NOT NULL AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('master_admin', 'company_admin', 'user') NOT NULL DEFAULT 'user',
    company_id INT(11) DEFAULT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL DEFAULT NULL,
    status ENUM('active', 'inactive', 'suspended') NOT NULL DEFAULT 'active',
    created_by INT(11) DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY unique_email_company (email, company_id),
    KEY idx_email (email),
    KEY idx_company_id (company_id),
    KEY idx_role (role),
    KEY idx_status (status),
    KEY fk_users_created_by (created_by),
    CONSTRAINT fk_users_company FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    CONSTRAINT fk_users_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User rights table for module permissions
CREATE TABLE user_rights (
    id INT(11) NOT NULL AUTO_INCREMENT,
    user_id INT(11) NOT NULL,
    module_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY unique_user_module (user_id, module_name),
    KEY idx_user_id (user_id),
    KEY idx_module_name (module_name),
    CONSTRAINT fk_user_rights_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Payments table for tracking invoice payments
CREATE TABLE payments (
    id INT(11) NOT NULL AUTO_INCREMENT,
    user_id INT(11) NOT NULL,
    company_id INT(11) NOT NULL,
    invoice_number VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('bank_transfer', 'credit_card', 'cash', 'cheque') NOT NULL DEFAULT 'bank_transfer',
    payment_status ENUM('pending', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    payment_reference VARCHAR(255) DEFAULT NULL,
    payment_date TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT(11) DEFAULT NULL,
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_company_id (company_id),
    KEY idx_invoice_number (invoice_number),
    KEY idx_payment_status (payment_status),
    KEY idx_payment_date (payment_date),
    KEY fk_payments_created_by (created_by),
    CONSTRAINT fk_payments_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_payments_company FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    CONSTRAINT fk_payments_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User sessions table for session management
CREATE TABLE user_sessions (
    id INT(11) NOT NULL AUTO_INCREMENT,
    user_id INT(11) NOT NULL,
    session_id VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    PRIMARY KEY (id),
    UNIQUE KEY unique_session_id (session_id),
    KEY idx_user_id (user_id),
    KEY idx_expires_at (expires_at),
    KEY idx_is_active (is_active),
    CONSTRAINT fk_user_sessions_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Activity logs table for audit trail
CREATE TABLE activity_logs (
    id INT(11) NOT NULL AUTO_INCREMENT,
    user_id INT(11) DEFAULT NULL,
    company_id INT(11) DEFAULT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT DEFAULT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_company_id (company_id),
    KEY idx_action (action),
    KEY idx_created_at (created_at),
    CONSTRAINT fk_activity_logs_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    CONSTRAINT fk_activity_logs_company FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- INSERT SAMPLE DATA
-- ============================================================================

-- Insert default companies and users
-- Note: These are sample companies for testing the multi-tenant system
INSERT INTO companies (company_name, company_code, customs_code, address, telephone, email, ftp_folder_path, status) VALUES
('Master System', 'MASTER', 'MASTER001', 'System Administration', '+27-11-000-0000', '<EMAIL>', '/invoices/MASTER001/', 'active'),
('Demo Company Ltd', 'DEMO001', 'DEMO123456', '123 Demo Street, Demo City, 1234', '+27-11-123-4567', '<EMAIL>', '/invoices/DEMO123456/', 'active'),
('Sample Clearing Co', 'SAMPLE01', 'SAMPLE789', '456 Sample Avenue, Sample Town, 5678', '+27-21-987-6543', '<EMAIL>', '/invoices/SAMPLE789/', 'active');

-- Master Admin user (password: master123)
INSERT INTO users (name, email, password, role, company_id) VALUES
('Master Administrator', '<EMAIL>', '$2y$12$LTVXDglo.oxDbeCgcLoBXOOqvJT3XkvFkFew57YWkRrmH73hYBsZC', 'master_admin', 1);

-- Demo Company Admin (password: admin123)
INSERT INTO users (name, email, password, role, company_id, created_by) VALUES
('Demo Company Admin', '<EMAIL>', '$2y$12$39Nxqv7VLiBH.er7PylfNuu783noTlT9VH/WFWIL7/wRsU3wBQ7i2', 'company_admin', 2, 1);

-- Sample Company Admin (password: admin123)
INSERT INTO users (name, email, password, role, company_id, created_by) VALUES
('Sample Company Admin', '<EMAIL>', '$2y$12$39Nxqv7VLiBH.er7PylfNuu783noTlT9VH/WFWIL7/wRsU3wBQ7i2', 'company_admin', 3, 1);

-- Demo regular users (password: admin123 for all)
INSERT INTO users (name, email, password, role, company_id, created_by) VALUES
('Demo User 1', '<EMAIL>', '$2y$12$39Nxqv7VLiBH.er7PylfNuu783noTlT9VH/WFWIL7/wRsU3wBQ7i2', 'user', 2, 2),
('Demo User 2', '<EMAIL>', '$2y$12$39Nxqv7VLiBH.er7PylfNuu783noTlT9VH/WFWIL7/wRsU3wBQ7i2', 'user', 2, 2),
('Sample User 1', '<EMAIL>', '$2y$12$39Nxqv7VLiBH.er7PylfNuu783noTlT9VH/WFWIL7/wRsU3wBQ7i2', 'user', 3, 3);

-- Grant all module access to master admin
INSERT INTO user_rights (user_id, module_name) VALUES
(1, 'invoices'),
(1, 'freight'),
(1, 'tariff'),
(1, 'accounting'); 

-- Grant basic access to demo users
INSERT INTO user_rights (user_id, module_name) VALUES
(2, 'invoices'),
(2, 'freight'),
(3, 'invoices'),
(3, 'freight'),
(4, 'invoices'),
(5, 'invoices'),
(6, 'invoices');

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

-- Database reset completed successfully!
-- You can now use the application with the sample data.

COMMIT;
