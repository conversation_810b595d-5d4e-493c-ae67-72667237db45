<?php
/**
 * Demo Status Page
 * Shows demo mode information and available test accounts
 */

require_once 'config/config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// If user is already logged in, redirect to dashboard
if (isset($_SESSION['user_id']) && !empty($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit();
}

// If not in demo mode, redirect to login
if (!defined('FTP_DEMO_MODE') || !FTP_DEMO_MODE) {
    header('Location: login.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Mode - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .demo-title {
            color: #2B5E5F;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .demo-subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }
        
        .demo-badge {
            background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 100%);
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 1rem;
        }
        
        .accounts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .account-card {
            background: #f8fafc;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 2rem;
            transition: all 0.3s ease;
        }
        
        .account-card:hover {
            border-color: #2B5E5F;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(43, 94, 95, 0.15);
        }
        
        .account-type {
            color: #2B5E5F;
            font-weight: 700;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .account-details {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2B5E5F;
        }
        
        .credential-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-family: 'Courier New', monospace;
        }
        
        .credential-label {
            font-weight: 600;
            color: #374151;
        }
        
        .credential-value {
            color: #2B5E5F;
            font-weight: 600;
        }
        
        .customs-code-highlight {
            background: #2B5E5F;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-weight: 700;
        }
        
        .login-button {
            background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 100%);
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .login-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(43, 94, 95, 0.3);
        }
        
        .features-section {
            background: #f8fafc;
            padding: 2rem;
            border-radius: 12px;
            margin-top: 2rem;
        }
        
        .features-title {
            color: #2B5E5F;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .feature-item {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #2B5E5F;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .feature-name {
            font-weight: 600;
            color: #2B5E5F;
            margin-bottom: 0.5rem;
        }
        
        .feature-desc {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <div class="demo-badge">🚀 DEMO MODE ACTIVE</div>
            <h1 class="demo-title">FTP Portal Demo</h1>
            <p class="demo-subtitle">Experience the full functionality with test accounts and sample data</p>
        </div>

        <div class="accounts-grid">
            <!-- Master Admin Account -->
            <div class="account-card">
                <div class="account-type">
                    👑 Master Administrator
                </div>
                <div class="account-details">
                    <div class="credential-row">
                        <span class="credential-label">Email:</span>
                        <span class="credential-value"><EMAIL></span>
                    </div>
                    <div class="credential-row">
                        <span class="credential-label">Password:</span>
                        <span class="credential-value">master123</span>
                    </div>
                    <div class="credential-row">
                        <span class="credential-label">Company Code:</span>
                        <span class="credential-value">MASTER</span>
                    </div>
                    <div class="credential-row">
                        <span class="credential-label">Customs Code:</span>
                        <span class="customs-code-highlight">MASTER001</span>
                    </div>
                </div>
                <p style="color: #666; margin-bottom: 1.5rem; font-size: 0.9rem;">
                    Full system access including FTP browser, user management, and system administration.
                </p>
                <a href="login.php" class="login-button">Login as Master Admin</a>
            </div>

            <!-- Company Admin Account -->
            <div class="account-card">
                <div class="account-type">
                    🏢 Company Administrator
                </div>
                <div class="account-details">
                    <div class="credential-row">
                        <span class="credential-label">Email:</span>
                        <span class="credential-value"><EMAIL></span>
                    </div>
                    <div class="credential-row">
                        <span class="credential-label">Password:</span>
                        <span class="credential-value">admin123</span>
                    </div>
                    <div class="credential-row">
                        <span class="credential-label">Company Code:</span>
                        <span class="credential-value">DEMO001</span>
                    </div>
                    <div class="credential-row">
                        <span class="credential-label">Customs Code:</span>
                        <span class="customs-code-highlight">DEMO123456</span>
                    </div>
                </div>
                <p style="color: #666; margin-bottom: 1.5rem; font-size: 0.9rem;">
                    Company-level administration including user management and company settings.
                </p>
                <a href="login.php" class="login-button">Login as Company Admin</a>
            </div>

            <!-- Regular User Account -->
            <div class="account-card">
                <div class="account-type">
                    👤 Regular User
                </div>
                <div class="account-details">
                    <div class="credential-row">
                        <span class="credential-label">Email:</span>
                        <span class="credential-value"><EMAIL></span>
                    </div>
                    <div class="credential-row">
                        <span class="credential-label">Password:</span>
                        <span class="credential-value">admin123</span>
                    </div>
                    <div class="credential-row">
                        <span class="credential-label">Company Code:</span>
                        <span class="credential-value">DEMO001</span>
                    </div>
                    <div class="credential-row">
                        <span class="credential-label">Customs Code:</span>
                        <span class="customs-code-highlight">DEMO123456</span>
                    </div>
                </div>
                <p style="color: #666; margin-bottom: 1.5rem; font-size: 0.9rem;">
                    Standard user access to invoices, documents, and company-specific FTP folder.
                </p>
                <a href="login.php" class="login-button">Login as User</a>
            </div>
        </div>

        <div class="features-section">
            <h2 class="features-title">🎯 Demo Features Available</h2>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">🗂️</div>
                    <div class="feature-name">FTP Browser</div>
                    <div class="feature-desc">Browse company FTP folders using customs codes</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📄</div>
                    <div class="feature-name">Invoice Management</div>
                    <div class="feature-desc">View and download sample invoices</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">👥</div>
                    <div class="feature-name">User Management</div>
                    <div class="feature-desc">Manage users and permissions</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔍</div>
                    <div class="feature-name">System Validation</div>
                    <div class="feature-desc">Validate FTP connectivity and customs codes</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🏢</div>
                    <div class="feature-name">Multi-Tenant</div>
                    <div class="feature-desc">Company-based access with customs codes</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔒</div>
                    <div class="feature-name">Security Controls</div>
                    <div class="feature-desc">Role-based access and FTP isolation</div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 3rem; padding-top: 2rem; border-top: 2px solid #e5e7eb;">
            <p style="color: #666; margin-bottom: 1rem;">
                <strong>Note:</strong> This is a demonstration environment with sample data. 
                All FTP operations use simulated data for testing purposes.
            </p>
            <a href="login.php" class="login-button" style="font-size: 1.1rem; padding: 1rem 3rem;">
                🚀 Start Demo Experience
            </a>
        </div>
    </div>
</body>
</html>
