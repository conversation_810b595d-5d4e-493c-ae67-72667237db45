<?php
/**
 * FTP Manager Class
 * Handles FTP operations for user files with dynamic credentials
 */

require_once __DIR__ . '/../config/config.php';

class FTPManager {
    private $connection;
    private $host;
    private $port;
    private $username;
    private $password;
    private $customs_code;
    private $allowed_base_paths;

    public function __construct($username = null, $password = null, $customs_code = null) {
        $this->host = FTP_HOST;
        $this->port = FTP_PORT;
        $this->customs_code = $customs_code;

        // Use provided credentials or session credentials
        if ($username && $password) {
            $this->username = $username;
            $this->password = $password;
        } elseif (isset($_SESSION['ftp_username']) && isset($_SESSION['ftp_password'])) {
            $this->username = $_SESSION['ftp_username'];
            $this->password = $_SESSION['ftp_password'];
        } else {
            throw new Exception('FTP credentials not provided');
        }

        // Set customs code from session if not provided
        if (!$this->customs_code && isset($_SESSION['customs_code'])) {
            $this->customs_code = $_SESSION['customs_code'];
        }

        // Initialize allowed base paths for this user
        $this->initializeAllowedPaths();
    }

    /**
     * Initialize allowed FTP paths for the current user
     */
    private function initializeAllowedPaths() {
        $this->allowed_base_paths = [];

        if ($this->customs_code) {
            // User can only access their company's folder based on customs code
            $this->allowed_base_paths[] = '/invoices/' . $this->customs_code . '/';
            $this->allowed_base_paths[] = '/invoices/' . $this->customs_code;
        }

        // For admin users (master admin), allow broader access
        if (isset($_SESSION['role']) && $_SESSION['role'] === 'master_admin') {
            $this->allowed_base_paths[] = '/invoices/';
            $this->allowed_base_paths[] = '/';
        }
    }

    /**
     * Validate if a path is allowed for the current user
     */
    private function validatePathAccess($path) {
        // Normalize path
        $normalized_path = '/' . trim($path, '/') . '/';

        // For master admin, allow all access
        if (isset($_SESSION['role']) && $_SESSION['role'] === 'master_admin') {
            return true;
        }

        // Check if path starts with any allowed base path
        foreach ($this->allowed_base_paths as $allowed_path) {
            if (strpos($normalized_path, $allowed_path) === 0) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Connect to FTP server
     */
    public function connect() {
        $this->connection = ftp_connect($this->host, $this->port, FTP_TIMEOUT);

        if (!$this->connection) {
            throw new Exception('Could not connect to FTP server');
        }

        if (!ftp_login($this->connection, $this->username, $this->password)) {
            throw new Exception('FTP login failed');
        }

        // Set passive mode
        ftp_pasv($this->connection, true);

        return true;
    }
    
    /**
     * Disconnect from FTP server
     */
    public function disconnect() {
        if ($this->connection) {
            ftp_close($this->connection);
        }
    }
    
    /**
     * Get list of files in client's folder
     */
    public function getClientFiles($client_folder_path) {
        if (!$this->connection) {
            $this->connect();
        }

        // Ensure folder path starts with /
        if (substr($client_folder_path, 0, 1) !== '/') {
            $client_folder_path = '/' . $client_folder_path;
        }

        // Validate path access for security
        if (!$this->validatePathAccess($client_folder_path)) {
            throw new Exception('Access denied: You do not have permission to access this directory');
        }

        // Get file list
        $files = ftp_nlist($this->connection, $client_folder_path);
        
        if ($files === false) {
            // If folder doesn't exist or is empty, return empty array
            return [];
        }
        
        $invoice_files = [];
        
        foreach ($files as $file) {
            // Get just the filename
            $filename = basename($file);
            
            // Skip directories and non-PDF files
            if ($filename === '.' || $filename === '..' || !$this->isPDFFile($filename)) {
                continue;
            }
            
            // Get file details
            $file_info = $this->getFileInfo($file);
            
            $invoice_files[] = [
                'filename' => $filename,
                'full_path' => $file,
                'size' => $file_info['size'],
                'modified' => $file_info['modified']
            ];
        }
        
        // Sort by modification date (newest first)
        usort($invoice_files, function($a, $b) {
            return $b['modified'] - $a['modified'];
        });
        
        return $invoice_files;
    }
    
    /**
     * Download file from FTP
     */
    public function downloadFile($remote_file, $local_file) {
        if (!$this->connection) {
            $this->connect();
        }

        // Validate path access for security
        $file_directory = dirname($remote_file);
        if (!$this->validatePathAccess($file_directory)) {
            throw new Exception('Access denied: You do not have permission to access this file');
        }

        return ftp_get($this->connection, $local_file, $remote_file, FTP_BINARY);
    }
    
    /**
     * Upload file to FTP
     */
    public function uploadFile($local_file, $remote_file) {
        if (!$this->connection) {
            $this->connect();
        }

        // Validate path access for security
        $file_directory = dirname($remote_file);
        if (!$this->validatePathAccess($file_directory)) {
            throw new Exception('Access denied: You do not have permission to upload to this directory');
        }

        return ftp_put($this->connection, $remote_file, $local_file, FTP_BINARY);
    }
    
    /**
     * Create payment success file
     */
    public function createPaymentSuccessFile($client_folder_path, $invoice_filename, $payment_data) {
        if (!$this->connection) {
            $this->connect();
        }
        
        // Create success file content
        $success_content = $this->generateSuccessFileContent($payment_data);
        
        // Create temporary local file
        $temp_file = tempnam(sys_get_temp_dir(), 'payment_success_');
        file_put_contents($temp_file, $success_content);
        
        // Generate success filename
        $success_filename = $this->generateSuccessFilename($invoice_filename);
        $remote_path = rtrim($client_folder_path, '/') . '/' . $success_filename;
        
        // Upload to FTP
        $result = $this->uploadFile($temp_file, $remote_path);
        
        // Clean up temporary file
        unlink($temp_file);
        
        return $result;
    }
    
    /**
     * Check if file is PDF
     */
    private function isPDFFile($filename) {
        return strtolower(pathinfo($filename, PATHINFO_EXTENSION)) === 'pdf';
    }
    
    /**
     * Get file information
     */
    private function getFileInfo($file) {
        $size = ftp_size($this->connection, $file);
        $modified = ftp_mdtm($this->connection, $file);
        
        return [
            'size' => $size !== -1 ? $size : 0,
            'modified' => $modified !== -1 ? $modified : time()
        ];
    }
    
    /**
     * Generate success file content
     */
    private function generateSuccessFileContent($payment_data) {
        $content = "PAYMENT SUCCESS NOTIFICATION\n";
        $content .= "============================\n\n";
        $content .= "Invoice: " . $payment_data['invoice_name'] . "\n";
        $content .= "Payment Reference: " . $payment_data['payment_reference'] . "\n";
        $content .= "Amount: R" . number_format($payment_data['amount'], 2) . "\n";
        $content .= "Payment Date: " . date('Y-m-d H:i:s') . "\n";
        $content .= "Transaction ID: " . $payment_data['transaction_id'] . "\n";
        $content .= "Status: PAID\n\n";
        $content .= "This file confirms successful payment for the above invoice.\n";
        
        return $content;
    }
    
    /**
     * Generate success filename
     */
    private function generateSuccessFilename($invoice_filename) {
        $name_without_ext = pathinfo($invoice_filename, PATHINFO_FILENAME);
        return $name_without_ext . '_PAID_' . date('Ymd_His') . '.txt';
    }
    
    /**
     * Check if folder exists
     */
    public function folderExists($folder_path) {
        if (!$this->connection) {
            $this->connect();
        }
        
        $current_dir = ftp_pwd($this->connection);
        $result = @ftp_chdir($this->connection, $folder_path);
        
        if ($result) {
            ftp_chdir($this->connection, $current_dir);
        }
        
        return $result;
    }
    
    /**
     * Create folder if it doesn't exist
     */
    public function createFolder($folder_path) {
        if (!$this->connection) {
            $this->connect();
        }
        
        if ($this->folderExists($folder_path)) {
            return true;
        }
        
        return ftp_mkdir($this->connection, $folder_path);
    }
}
?>
