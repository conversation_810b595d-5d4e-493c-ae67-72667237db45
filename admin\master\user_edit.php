<?php
/**
 * User Edit Page - Master Admin
 * Allows master admin to edit user details and manage module permissions
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../shared/admin_config.php';

// Require master admin login
requireLogin();
if ($_SESSION['role'] !== 'master_admin') {
    header('Location: ../../login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

$error_message = '';
$success_message = '';
$user_id = $_GET['id'] ?? 0;

// Get user details
$query = "SELECT u.*, c.company_name, c.company_code, c.customs_code
          FROM users u
          LEFT JOIN companies c ON u.company_id = c.id
          WHERE u.id = ? AND u.status != 'deleted'";
$stmt = $db->prepare($query);
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: companies.php?error=User not found');
    exit();
}

// Get available modules
$available_modules = [
    'invoices' => 'Commercial Invoices',
    'freight' => 'Freight Management', 
    'tariff' => 'Customs Tariff',
    'accounting' => 'Trade Finance',
    'backoffice' => 'Back Office'
];

// Get user's current module permissions
$query = "SELECT module_name FROM user_rights WHERE user_id = ?";
$stmt = $db->prepare($query);
$stmt->execute([$user_id]);
$user_modules = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Get all companies for dropdown
$query = "SELECT id, company_name FROM companies WHERE status = 'active' ORDER BY company_name";
$stmt = $db->prepare($query);
$stmt->execute();
$companies = $stmt->fetchAll();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCsrfToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'update_user') {
            $name = trim($_POST['name'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $role = $_POST['role'] ?? '';
            $company_id = $_POST['company_id'] ?? null;
            $status = $_POST['status'] ?? '';
            $selected_modules = $_POST['modules'] ?? [];
            
            // Validate required fields
            if (empty($name) || empty($email) || empty($role) || empty($status)) {
                $error_message = 'Name, email, role, and status are required.';
            } else {
                try {
                    $db->beginTransaction();
                    
                    // Check if email is already in use by another user
                    $check_query = "SELECT id FROM users WHERE email = ? AND id != ? AND company_id = ?";
                    $check_stmt = $db->prepare($check_query);
                    $check_stmt->execute([$email, $user_id, $company_id]);
                    
                    if ($check_stmt->fetchColumn()) {
                        $error_message = 'This email address is already in use by another user in the same company.';
                    } else {
                        // Update user details
                        $update_query = "UPDATE users SET name = ?, email = ?, role = ?, company_id = ?, status = ?, updated_at = NOW() WHERE id = ?";
                        $update_stmt = $db->prepare($update_query);
                        $update_stmt->execute([$name, $email, $role, $company_id, $status, $user_id]);
                        
                        // Update module permissions
                        // First, remove all existing permissions
                        $delete_query = "DELETE FROM user_rights WHERE user_id = ?";
                        $delete_stmt = $db->prepare($delete_query);
                        $delete_stmt->execute([$user_id]);
                        
                        // Then add selected permissions
                        if (!empty($selected_modules)) {
                            $insert_query = "INSERT INTO user_rights (user_id, module_name) VALUES (?, ?)";
                            $insert_stmt = $db->prepare($insert_query);
                            
                            foreach ($selected_modules as $module) {
                                if (array_key_exists($module, $available_modules)) {
                                    $insert_stmt->execute([$user_id, $module]);
                                }
                            }
                        }
                        
                        $db->commit();
                        $success_message = 'User updated successfully.';
                        
                        // Log activity
                        logActivity('user_updated', "User {$name} (ID: {$user_id}) updated", $_SESSION['user_id']);
                        
                        // Refresh user data
                        $stmt = $db->prepare($query);
                        $stmt->execute([$user_id]);
                        $user = $stmt->fetch();
                        
                        // Refresh user modules
                        $query = "SELECT module_name FROM user_rights WHERE user_id = ?";
                        $stmt = $db->prepare($query);
                        $stmt->execute([$user_id]);
                        $user_modules = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    }
                } catch (Exception $e) {
                    $db->rollBack();
                    $error_message = 'Failed to update user. Please try again.';
                    error_log("User update error: " . $e->getMessage());
                }
            }
        }
    }
}

$csrf_token = generateCsrfToken();
$page_title = "Edit User - " . htmlspecialchars($user['name']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('master'); ?>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'master', 'companies.php'); ?>
    
    <main class="admin-main">
        <div class="admin-content">
            <div class="content-header">
                <div class="header-left">
                    <h1>Edit User</h1>
                    <p>Manage user details and module permissions</p>
                </div>
                <div class="header-right">
                    <a href="companies.php" class="btn btn-secondary">← Back to Companies</a>
                </div>
            </div>

            <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
            <?php endif; ?>

            <div class="admin-card">
                <div class="card-header">
                    <h3>User Information</h3>
                    <p>Update user details and configure module access permissions</p>
                </div>
                
                <form method="POST" class="user-edit-form">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="update_user">
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="name" class="form-label">Full Name *</label>
                            <input type="text" id="name" name="name" class="form-input" 
                                   value="<?php echo htmlspecialchars($user['name']); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" id="email" name="email" class="form-input" 
                                   value="<?php echo htmlspecialchars($user['email']); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="role" class="form-label">Role *</label>
                            <select id="role" name="role" class="form-select" required>
                                <option value="">Select Role</option>
                                <option value="master_admin" <?php echo $user['role'] === 'master_admin' ? 'selected' : ''; ?>>Master Admin</option>
                                <option value="company_admin" <?php echo $user['role'] === 'company_admin' ? 'selected' : ''; ?>>Company Admin</option>
                                <option value="user" <?php echo $user['role'] === 'user' ? 'selected' : ''; ?>>User</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="company_id" class="form-label">Company</label>
                            <select id="company_id" name="company_id" class="form-select">
                                <option value="">No Company</option>
                                <?php foreach ($companies as $company): ?>
                                    <option value="<?php echo $company['id']; ?>" 
                                            <?php echo $user['company_id'] == $company['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($company['company_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="status" class="form-label">Status *</label>
                            <select id="status" name="status" class="form-select" required>
                                <option value="">Select Status</option>
                                <option value="active" <?php echo $user['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                <option value="inactive" <?php echo $user['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                <option value="suspended" <?php echo $user['status'] === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="customs_code" class="form-label">Company Customs Code</label>
                            <input type="text" id="customs_code" class="form-input"
                                   value="<?php echo htmlspecialchars($user['customs_code'] ?? 'Not Assigned'); ?>"
                                   disabled
                                   style="font-family: 'Courier New', monospace; font-weight: 600; background: #f8fafc; color: #2B5E5F;">
                            <small style="color: #666; font-size: 0.85rem;">This is the company's customs code for FTP access.</small>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h4>Module Permissions</h4>
                        <p>Select which modules this user can access</p>
                        
                        <div class="modules-grid">
                            <?php foreach ($available_modules as $module_key => $module_name): ?>
                                <div class="module-permission">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="modules[]" value="<?php echo $module_key; ?>" 
                                               <?php echo in_array($module_key, $user_modules) ? 'checked' : ''; ?>>
                                        <span class="checkbox-custom"></span>
                                        <span class="module-name"><?php echo htmlspecialchars($module_name); ?></span>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Update User</button>
                        <a href="companies.php" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <style>
        .user-edit-form {
            max-width: 800px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-input, .form-select {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.2s ease;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #2B5E5F;
            box-shadow: 0 0 0 3px rgba(43, 94, 95, 0.1);
        }

        .form-section {
            margin: 2rem 0;
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .form-section h4 {
            margin: 0 0 0.5rem 0;
            color: #2B5E5F;
            font-size: 1.1rem;
        }

        .form-section p {
            margin: 0 0 1.5rem 0;
            color: #6b7280;
            font-size: 0.9rem;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .module-permission {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease;
        }

        .module-permission:hover {
            border-color: #2B5E5F;
            box-shadow: 0 2px 8px rgba(43, 94, 95, 0.1);
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: 500;
        }

        .checkbox-label input[type="checkbox"] {
            display: none;
        }

        .checkbox-custom {
            width: 20px;
            height: 20px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            margin-right: 0.75rem;
            position: relative;
            transition: all 0.2s ease;
        }

        .checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
            background: #2B5E5F;
            border-color: #2B5E5F;
        }

        .checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .module-name {
            color: #374151;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            padding-top: 2rem;
            border-top: 1px solid #e5e7eb;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .modules-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>
