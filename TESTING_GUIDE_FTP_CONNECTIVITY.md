# FTP Connectivity Testing Guide

This guide provides comprehensive testing procedures for the newly implemented FTP connectivity functionality with user-specific identification codes.

## Prerequisites

1. **Database Setup**: Ensure the database schema includes the FTP identification code field
2. **FTP Server**: Have an FTP server configured for testing
3. **Test Data**: Sample users with different roles and companies

## Database Migration

Before testing, run the FTP identification code migration:

```sql
-- Run this SQL script to add FTP identification codes to existing users
-- File: database/migrations/add_ftp_identification_codes.sql
```

## Test Scenarios

### 1. User FTP Identification Code Generation

**Test Case 1.1: New User Creation**
- **Action**: Create a new user through admin interface
- **Expected**: User automatically gets a unique FTP identification code
- **Format**: `{COMPANY_CODE}{ROLE_PREFIX}{NUMBER}` (e.g., `DEMO001U01`)
- **Verification**: Check user profile and admin interfaces show the code

**Test Case 1.2: Code Uniqueness**
- **Action**: Create multiple users in the same company
- **Expected**: Each user gets a unique FTP identification code
- **Verification**: No duplicate codes exist in the database

### 2. Login Interface FTP Code Display

**Test Case 2.1: Dashboard Display**
- **Action**: Login as a regular user
- **Expected**: FTP code displayed in user menu and welcome banner
- **Location**: Header user menu and dashboard welcome section

**Test Case 2.2: Admin Interface Display**
- **Action**: Login as admin
- **Expected**: FTP code visible in admin header menu
- **Verification**: Code matches user's assigned code

### 3. User-Specific FTP Access Controls

**Test Case 3.1: User Folder Access**
- **Action**: User attempts to access their FTP folder
- **Expected**: Access granted to `/invoices/{USER_FTP_CODE}/` only
- **Verification**: User cannot access other users' folders

**Test Case 3.2: Path Validation**
- **Action**: User attempts to access unauthorized paths
- **Expected**: Access denied with security error
- **Test Paths**: 
  - `/invoices/OTHER_USER_CODE/`
  - `/invoices/../`
  - `/system/`

**Test Case 3.3: File Download Security**
- **Action**: User attempts to download files
- **Expected**: Can only download from their own folder
- **Verification**: Downloads from other folders are blocked

### 4. Master Admin FTP Management

**Test Case 4.1: FTP Browser Access**
- **Action**: Master admin accesses FTP browser
- **Expected**: Can browse any user's FTP folder
- **URL**: `/admin/master/ftp_browser.php`

**Test Case 4.2: User Selection**
- **Action**: Select different users from dropdown
- **Expected**: Shows user's FTP code and folder contents
- **Verification**: Correct folder path based on FTP code

**Test Case 4.3: File Management**
- **Action**: Download files through admin interface
- **Expected**: Successful download with proper security logging
- **Verification**: Activity logs record admin file access

### 5. Company Admin FTP Management

**Test Case 5.1: User FTP Codes Display**
- **Action**: Company admin views users table
- **Expected**: FTP codes displayed for all company users
- **Location**: `/admin/company/users.php`

**Test Case 5.2: FTP Browser Link**
- **Action**: Click FTP link for a user
- **Expected**: Redirects to master admin FTP browser for that user
- **Verification**: Correct user_id parameter passed

### 6. Profile and Settings Integration

**Test Case 6.1: User Profile Display**
- **Action**: User views their profile
- **Expected**: FTP code displayed in account details section
- **Location**: `/profile.php`
- **Style**: Monospace font, highlighted display

**Test Case 6.2: Admin User Edit**
- **Action**: Admin edits user details
- **Expected**: FTP code visible but not editable
- **Location**: `/admin/master/user_edit.php`

### 7. FTP Connectivity and File Operations

**Test Case 7.1: Real FTP Connection**
- **Action**: Configure real FTP server credentials
- **Expected**: Successful connection using user's FTP code
- **Verification**: Files listed from correct user folder

**Test Case 7.2: File Upload Security**
- **Action**: Attempt file upload to user folder
- **Expected**: Upload only allowed to user's designated folder
- **Verification**: Path validation prevents unauthorized uploads

**Test Case 7.3: Demo Mode Compatibility**
- **Action**: Test with FTP_DEMO_MODE enabled
- **Expected**: System works with demo files
- **Verification**: FTP codes still displayed and tracked

## Security Testing

### 8. Access Control Validation

**Test Case 8.1: Role-Based Access**
- **Users**: Test with different roles (user, company_admin, master_admin)
- **Expected**: Appropriate access levels for each role
- **Verification**: Master admin can access all, others restricted

**Test Case 8.2: Session Security**
- **Action**: Test with invalid or expired sessions
- **Expected**: Proper authentication required
- **Verification**: No unauthorized FTP access

**Test Case 8.3: Path Traversal Prevention**
- **Action**: Attempt directory traversal attacks
- **Test Inputs**: `../`, `..\\`, encoded paths
- **Expected**: All attempts blocked with security logging

## Performance Testing

### 9. System Performance

**Test Case 9.1: FTP Code Generation Speed**
- **Action**: Create multiple users rapidly
- **Expected**: Fast code generation without conflicts
- **Metrics**: < 1 second per user creation

**Test Case 9.2: FTP Connection Performance**
- **Action**: Multiple concurrent FTP operations
- **Expected**: Stable performance under load
- **Verification**: No connection timeouts or errors

## Error Handling

### 10. Error Scenarios

**Test Case 10.1: FTP Server Unavailable**
- **Action**: Test with FTP server down
- **Expected**: Graceful error handling with user-friendly messages
- **Verification**: System remains stable

**Test Case 10.2: Invalid FTP Credentials**
- **Action**: Test with wrong FTP credentials
- **Expected**: Clear error messages, no system crashes
- **Verification**: Security events logged

**Test Case 10.3: Missing FTP Code**
- **Action**: Test with user lacking FTP identification code
- **Expected**: System handles gracefully, offers code generation
- **Verification**: No fatal errors

## Deployment Preparation

### 11. Pre-Deployment Checklist

- [ ] All database migrations applied
- [ ] FTP identification codes generated for existing users
- [ ] FTP server configurations tested
- [ ] Security logging enabled
- [ ] Error handling verified
- [ ] Performance benchmarks met
- [ ] Documentation updated
- [ ] Admin training materials prepared

### 12. Post-Deployment Monitoring

**Monitor These Metrics:**
- FTP connection success rates
- User access pattern compliance
- Security event frequency
- System performance metrics
- Error rates and types

## Test Data Setup

### Sample Test Users

```sql
-- Create test users with different roles and companies
-- These should automatically get FTP identification codes

INSERT INTO companies (company_name, company_code, ftp_folder_path, status) VALUES
('Test Company A', 'TESTA', '/invoices/testa/', 'active'),
('Test Company B', 'TESTB', '/invoices/testb/', 'active');

-- Users will be created through the interface to test FTP code generation
```

### Expected FTP Code Patterns

- Master Admin: `MASTER001`, `MASTER002`, etc.
- Company Admin: `TESTAAADM01`, `TESTBADM01`, etc.
- Regular Users: `TESTAU01`, `TESTAU02`, `TESTBU01`, etc.

## Troubleshooting

### Common Issues

1. **Missing FTP Codes**: Run migration script
2. **Access Denied**: Check user permissions and FTP assignments
3. **Connection Errors**: Verify FTP server configuration
4. **Path Issues**: Ensure proper folder structure on FTP server

### Debug Tools

- Check activity logs for security events
- Verify database FTP identification codes
- Test FTP connections manually
- Review error logs for detailed information

## Success Criteria

✅ All users have unique FTP identification codes
✅ User isolation is enforced (users can only access their folders)
✅ Admin interfaces show FTP codes correctly
✅ FTP connectivity works with real servers
✅ Security controls prevent unauthorized access
✅ Performance meets requirements
✅ Error handling is robust
✅ System is ready for production deployment

---

**Note**: This testing should be performed in a staging environment before production deployment. All test cases should pass before considering the system ready for live use.
